'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useLoading } from '@/components/providers/LoadingProvider';

export function useNavigationLoading() {
  const router = useRouter();
  const { setLoading } = useLoading();

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleStart = () => {
      // Set loading immediately on navigation start
      setLoading(true);

      // Clear any existing timeout
      if (timeoutId) clearTimeout(timeoutId);

      // Set a timeout to clear loading after 10 seconds as a fallback
      timeoutId = setTimeout(() => {
        setLoading(false);
      }, 10000);
    };

    const handleComplete = () => {
      // Clear loading on navigation complete
      setLoading(false);
      if (timeoutId) clearTimeout(timeoutId);
    };

    // Listen to router events
    window.addEventListener('beforeunload', handleStart);

    // For client-side navigation, we can use router events
    // Since Next.js doesn't expose navigation events directly, we'll use a different approach

    return () => {
      window.removeEventListener('beforeunload', handleStart);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [router, setLoading]);

  return {
    startLoading: () => setLoading(true),
    stopLoading: () => setLoading(false),
  };
}