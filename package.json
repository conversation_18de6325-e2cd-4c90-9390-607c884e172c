{"name": "ma<PERSON>o", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "seed": "tsx prisma/seed.ts", "upgrade-to-super-admin": "tsx prisma/upgrade-to-super-admin.ts"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@prisma/client": "^6.16.2", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^12.3.0", "firebase-admin": "^13.5.0", "lucide-react": "^0.544.0", "next": "15.5.4", "next-firebase-auth-edge": "^1.11.1", "next-intl": "^4.3.9", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.63.0", "recharts": "^3.2.1", "tailwind-merge": "^3.3.1", "zod": "^4.1.11", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.4", "prisma": "^6.16.2", "tailwindcss": "^4", "tsx": "^4.19.2", "tw-animate-css": "^1.4.0", "typescript": "^5"}}