'use client';

// components/providers/index.tsx
// Compose all providers together

import { ThemeProvider } from './ThemeProvider';
import { FirebaseAuthProvider } from './FirebaseAuthProvider';
import { CurrencyProvider } from './CurrencyProvider';
import { LoadingProvider } from './LoadingProvider';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider>
      <FirebaseAuthProvider>
        <CurrencyProvider>
          <LoadingProvider>
            {children}
          </LoadingProvider>
        </CurrencyProvider>
      </FirebaseAuthProvider>
    </ThemeProvider>
  );
}
