'use client';

// components/admin/PricingRulesTable.tsx
// Pricing rules table with CRUD operations

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { createPricingRule, deletePricingRule, updatePricingRule } from '@/lib/actions/admin/pricing.actions';
import type { PricingRule } from '@/lib/types/admin';

interface PricingRulesTableProps {
  rules: PricingRule[];
  locale: string;
}

export function PricingRulesTable({ rules, locale }: PricingRulesTableProps) {
  const tAdmin = useTranslations('admin');
  const tPricing = useTranslations('pricing');
  const router = useRouter();
  const [deleting, setDeleting] = useState<number | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [createForm, setCreateForm] = useState({
    rule_name: '',
    condition_type: 'GLOBAL',
    condition_value: '',
    markup_type: 'PERCENTAGE',
    markup_value: '',
    priority: '0',
    is_active: true,
  });

  const handleDelete = async (ruleId: number) => {
    if (!confirm(tAdmin('confirmDelete'))) return;

    setDeleting(ruleId);
    const result = await deletePricingRule(ruleId.toString());

    if (result.success) {
      router.refresh();
    } else {
      alert(result.error || tAdmin('deleteError'));
    }
    setDeleting(null);
  };

  const handleToggleActive = async (ruleId: number, currentStatus: boolean | null) => {
    const result = await updatePricingRule(ruleId.toString(), {
      is_active: !currentStatus,
    });

    if (result.success) {
      router.refresh();
    } else {
      alert(result.error || tAdmin('updateError'));
    }
  };

  const handleCreate = async () => {
    const result = await createPricingRule({
      rule_name: createForm.rule_name,
      condition_type: createForm.condition_type,
      condition_value: createForm.condition_value || undefined,
      markup_type: createForm.markup_type,
      markup_value: parseFloat(createForm.markup_value),
      priority: parseInt(createForm.priority),
      is_active: createForm.is_active,
    });

    if (result.success) {
      router.refresh();
      setShowCreateForm(false);
      setCreateForm({
        rule_name: '',
        condition_type: 'GLOBAL',
        condition_value: '',
        markup_type: 'PERCENTAGE',
        markup_value: '',
        priority: '0',
        is_active: true,
      });
    } else {
      alert(result.error || tAdmin('createError'));
    }
  };

  return (
    <div className="space-y-4">
      {/* Info Card */}
      <Card>
        <CardHeader>
          <CardTitle>{tPricing('rules')}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            {tPricing('rulesDescription')}
          </p>
        </CardContent>
      </Card>

      {/* Create Form */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>{tPricing('createRule')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="rule_name">{tPricing('ruleName')}</Label>
              <Input
                id="rule_name"
                value={createForm.rule_name}
                onChange={(e) => setCreateForm({ ...createForm, rule_name: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="condition_type">{tPricing('conditionType')}</Label>
              <Select value={createForm.condition_type} onValueChange={(value) => setCreateForm({ ...createForm, condition_type: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GLOBAL">{tPricing('global')}</SelectItem>
                  <SelectItem value="CATEGORY">{tPricing('category')}</SelectItem>
                  <SelectItem value="PRODUCT_ID">{tPricing('productId')}</SelectItem>
                  <SelectItem value="MARKETPLACE">{tPricing('marketplace')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="condition_value">{tPricing('conditionValue')}</Label>
              <Input
                id="condition_value"
                value={createForm.condition_value}
                onChange={(e) => setCreateForm({ ...createForm, condition_value: e.target.value })}
                placeholder="Leave empty for global"
              />
            </div>
            <div>
              <Label htmlFor="markup_type">{tPricing('markupType')}</Label>
              <Select value={createForm.markup_type} onValueChange={(value) => setCreateForm({ ...createForm, markup_type: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PERCENTAGE">{tPricing('percentage')}</SelectItem>
                  <SelectItem value="FIXED_AMOUNT_ADD">{tPricing('fixedAmountAdd')}</SelectItem>
                  <SelectItem value="FIXED_AMOUNT_SET">{tPricing('fixedAmountSet')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="markup_value">{tPricing('markupValue')}</Label>
              <Input
                id="markup_value"
                type="number"
                value={createForm.markup_value}
                onChange={(e) => setCreateForm({ ...createForm, markup_value: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="priority">{tPricing('priority')}</Label>
              <Input
                id="priority"
                type="number"
                value={createForm.priority}
                onChange={(e) => setCreateForm({ ...createForm, priority: e.target.value })}
              />
            </div>
            <div className="flex gap-2">
              <Button onClick={handleCreate}>{tPricing('createRule')}</Button>
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>{tAdmin('cancel')}</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Rules Table */}
      <div className="border rounded-lg">
        <table className="w-full">
          <thead className="bg-muted">
            <tr>
              <th className="text-left p-4 font-medium">{tPricing('priority')}</th>
              <th className="text-left p-4 font-medium">{tPricing('ruleName')}</th>
              <th className="text-left p-4 font-medium">{tPricing('markupType')}</th>
              <th className="text-left p-4 font-medium">{tPricing('markupValue')}</th>
              <th className="text-left p-4 font-medium">{tPricing('conditionType')}</th>
              <th className="text-left p-4 font-medium">{tAdmin('status')}</th>
              <th className="text-left p-4 font-medium">{tAdmin('actions')}</th>
            </tr>
          </thead>
          <tbody>
            {rules.length === 0 ? (
              <tr>
                <td colSpan={7} className="text-center py-8 text-muted-foreground">
                  {tPricing('noRules')}
                </td>
              </tr>
            ) : (
              rules.map((rule) => (
                <tr key={rule.id} className="border-t hover:bg-accent/50">
                  <td className="p-4">
                    <Badge variant="outline">{rule.priority}</Badge>
                  </td>
                  <td className="p-4">
                    <div>
                      <p className="font-medium">{rule.rule_name}</p>
                      <p className="text-sm text-muted-foreground">
                        {rule.condition_type}: {rule.condition_value || 'All'}
                      </p>
                    </div>
                  </td>
                  <td className="p-4 text-sm">{rule.markup_type}</td>
                  <td className="p-4">
                    <span className="font-medium">
                      {rule.markup_type.includes('PERCENTAGE') ? `${Number(rule.markup_value)}%` : `$${Number(rule.markup_value)}`}
                    </span>
                  </td>
                  <td className="p-4 text-sm">
                    <div className="space-y-1">
                      <p className="text-xs">Condition: {rule.condition_type}</p>
                      {rule.condition_value && (
                        <p className="text-xs">Value: {rule.condition_value}</p>
                      )}
                    </div>
                  </td>
                  <td className="p-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleActive(rule.id, rule.is_active)}
                    >
                      <Badge variant={rule.is_active ? 'default' : 'secondary'}>
                        {rule.is_active ? tPricing('active') : tPricing('inactive')}
                      </Badge>
                    </Button>
                  </td>
                  <td className="p-4">
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(rule.id)}
                        disabled={deleting === rule.id}
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Create Button */}
      <div className="flex justify-end">
        <Button onClick={() => setShowCreateForm(!showCreateForm)}>
          <Plus className="h-4 w-4 mr-2" />
          {showCreateForm ? tAdmin('cancel') : tPricing('createRule')}
        </Button>
      </div>

      {/* Note */}
      <Card>
        <CardContent className="pt-6">
          <p className="text-sm text-muted-foreground">
            {tPricing('rulesApplied')}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

