'use client';

// components/admin/ProductsTable.tsx
// Products table with search and pagination

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Search, Edit, Trash2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { deleteProduct } from '@/lib/actions/admin/product.actions';
import type { ProductAdminListItem } from '@/lib/types/admin';

interface ProductsTableProps {
  products: ProductAdminListItem[];
  total: number;
  page: number;
  totalPages: number;
  locale: string;
}

export function ProductsTable({ products, total, page, totalPages, locale }: ProductsTableProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const searchParams = useSearchParams();
  const [search, setSearch] = useState(searchParams.get('search') || '');
  const [deleting, setDeleting] = useState<number | null>(null);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(searchParams.toString());
    if (search) {
      params.set('search', search);
    } else {
      params.delete('search');
    }
    params.delete('page');
    router.push(`/${locale}/admin/products?${params.toString()}`);
  };

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', newPage.toString());
    router.push(`/${locale}/admin/products?${params.toString()}`);
  };

  const handleDelete = async (productId: number) => {
    if (!confirm(t('confirmDelete'))) return;

    setDeleting(productId);
    const result = await deleteProduct(productId);

    if (result.success) {
      router.refresh();
    } else {
      alert(result.error || t('deleteError'));
    }
    setDeleting(null);
  };

  return (
    <div className="space-y-4">
      {/* Search */}
      <form onSubmit={handleSearch} className="flex gap-2">
        <Input
          placeholder={t('searchProducts')}
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="max-w-sm"
        />
        <Button type="submit" variant="secondary">
          <Search className="h-4 w-4 mr-2" />
          {t('search')}
        </Button>
      </form>

      {/* Table */}
      <div className="border rounded-lg">
        <table className="w-full">
          <thead className="bg-muted">
            <tr>
              <th className="text-left p-4 font-medium">Image</th>
              <th className="text-left p-4 font-medium">Product Name</th>
              <th className="text-left p-4 font-medium">Categories</th>
              <th className="text-left p-4 font-medium">Status</th>
              <th className="text-left p-4 font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {products.length === 0 ? (
              <tr>
                <td colSpan={5} className="text-center py-8 text-muted-foreground">
                  {t('noProducts')}
                </td>
              </tr>
            ) : (
              products.map((product) => {
                const translation = product.translations?.[0];
                const image = product.product_images?.[0];

                return (
                  <tr key={product.id} className="border-t hover:bg-accent/50">
                    <td className="p-4">
                      {image ? (
                        <img
                          src={image.image_url}
                          alt={translation?.name || 'Product'}
                          className="w-16 h-16 object-cover rounded"
                        />
                      ) : (
                        <div className="w-16 h-16 bg-muted rounded flex items-center justify-center text-xs">
                          No image
                        </div>
                      )}
                    </td>
                    <td className="p-4">
                      <Link
                        href={`/${locale}/admin/products/${product.id}`}
                        className="font-medium hover:underline"
                      >
                        {translation?.name || 'Untitled'}
                      </Link>
                    </td>
                    <td className="p-4">
                      <div className="flex flex-wrap gap-1">
                        {product.categories?.slice(0, 2).map((cat) => (
                          <Badge key={cat.category.id} variant="outline" className="text-xs">
                            {cat.category.translations?.[0]?.name || cat.category.id}
                          </Badge>
                        ))}
                        {product.categories?.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{product.categories.length - 2}
                          </Badge>
                        )}
                      </div>
                    </td>
                    <td className="p-4">
                      <Badge variant={product.can_show ? 'default' : 'secondary'}>
                        {product.can_show ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td className="p-4">
                      <div className="flex gap-2">
                        <Link href={`/${locale}/admin/products/${product.id}`}>
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(product.id)}
                          disabled={deleting === product.id}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {(page - 1) * 24 + 1} to {Math.min(page * 24, total)} of {total} products
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={page === 1}
              onClick={() => handlePageChange(page - 1)}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={page === totalPages}
              onClick={() => handlePageChange(page + 1)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

