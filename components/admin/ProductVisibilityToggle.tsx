'use client';

// components/admin/ProductVisibilityToggle.tsx
// Toggle product visibility with optimistic updates

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Eye, EyeOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { updateProductVisibility } from '@/lib/actions/admin/product.actions';

interface ProductVisibilityToggleProps {
  productId: number;
  currentVisibility: boolean;
  locale: string;
}

export function ProductVisibilityToggle({
  productId,
  currentVisibility,
  locale
}: ProductVisibilityToggleProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(currentVisibility);
  const [loading, setLoading] = useState(false);

  const handleToggle = async () => {
    setLoading(true);
    
    // Optimistic update
    const newVisibility = !isVisible;
    setIsVisible(newVisibility);

    try {
      const result = await updateProductVisibility(productId, newVisibility);
      
      if (!result.success) {
        // Revert on error
        setIsVisible(isVisible);
        console.error('Failed to update visibility:', result.error);
      } else {
        // Refresh the page to show updated data
        router.refresh();
      }
    } catch (error) {
      // Revert on error
      setIsVisible(isVisible);
      console.error('Error updating visibility:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      variant={isVisible ? 'default' : 'outline'}
      size="sm"
      onClick={handleToggle}
      disabled={loading}
    >
      {isVisible ? (
        <>
          <Eye className="h-4 w-4 mr-2" />
          {t('visible')}
        </>
      ) : (
        <>
          <EyeOff className="h-4 w-4 mr-2" />
          {t('hidden')}
        </>
      )}
    </Button>
  );
}
