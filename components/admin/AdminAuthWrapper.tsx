'use client';

// components/admin/AdminAuthWrapper.tsx
// Client-side wrapper to handle admin authentication and session refresh

import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import { useAuth } from '@/components/providers/FirebaseAuthProvider';
import { checkAdminAccess } from '@/lib/actions/auth.actions';
import { AdminSidebar } from '@/components/admin/AdminSidebar';
import { AdminHeader } from '@/components/admin/AdminHeader';

interface AdminAuthWrapperProps {
  children: React.ReactNode;
  locale: string;
}

export function AdminAuthWrapper({ children, locale }: AdminAuthWrapperProps) {
  const { user, refreshSession } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAdminAccessWithSync = async () => {
      if (!user) {
        setIsAuthorized(false);
        setIsLoading(false);
        return;
      }

      try {
        // Check admin access
        const hasAccess = await checkAdminAccess();

        if (hasAccess) {
          setIsAuthorized(true);
          setIsLoading(false);
          return;
        }

        // If access check failed, try refreshing the session
        console.log('Admin access check failed, attempting session refresh...');
        const refreshSuccess = await refreshSession();

        if (refreshSuccess) {
          // Try checking admin access again after refresh
          const hasAccessAfterRefresh = await checkAdminAccess();
          setIsAuthorized(hasAccessAfterRefresh);
          if (hasAccessAfterRefresh) {
            console.log('Admin access restored after session refresh');
          }
        } else {
          console.error('Session refresh failed');
          setIsAuthorized(false);
        }
      } catch (error) {
        console.error('Error checking admin access:', error);
        setIsAuthorized(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminAccessWithSync();
  }, [user, refreshSession]);

  // Periodic session refresh for active admin users (every 30 minutes)
  useEffect(() => {
    if (!isAuthorized || !user) return;

    const refreshInterval = setInterval(async () => {
      try {
        console.log('Performing periodic session refresh for admin user');
        await refreshSession();
      } catch (error) {
        console.error('Periodic session refresh failed:', error);
      }
    }, 30 * 60 * 1000); // 30 minutes

    return () => clearInterval(refreshInterval);
  }, [isAuthorized, user, refreshSession]);

  // Show loading while checking authorization
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Checking permissions...</p>
        </div>
      </div>
    );
  }

  // Redirect if not authorized
  if (!isAuthorized) {
    if (!user) {
      // Not authenticated, redirect to login
      redirect(`/${locale}/login?redirect=/${locale}/admin`);
    } else {
      // Authenticated but not authorized, redirect to account
      redirect(`/${locale}/account`);
    }
  }

  // Render admin content
  return (
    <div className="flex min-h-screen bg-background">
      <AdminSidebar locale={locale} />

      <div className="flex-1 flex flex-col">
        <AdminHeader locale={locale} userName={user?.displayName || user?.email?.split('@')[0] || 'User'} />

        <main className="flex-1 p-8 bg-background">
          {children}
        </main>
      </div>
    </div>
  );
}