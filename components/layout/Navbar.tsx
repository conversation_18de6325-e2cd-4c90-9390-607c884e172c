'use client';

// components/layout/Navbar.tsx
// Professional navigation bar with modern design, search, and mobile menu

import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import {
  ShoppingCart,
  User,
  Menu,
  X,
  Search,
  Sun,
  Moon,
  Store,
  Package,
  Heart,
  LogOut,
  Settings
} from 'lucide-react';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { useCartStore } from '@/hooks/use-cart-store';
import { useAuth } from '@/components/providers/FirebaseAuthProvider';
import { CurrencySwitcher } from '@/components/ui/currency-switcher';
import { LoadingLink } from '@/components/ui/loading-link';
import { checkAdminAccess, validateSession } from '@/lib/actions/auth.actions';
import { useState, useEffect } from 'react';

export function Navbar() {
  const t = useTranslations('nav');
  const tCommon = useTranslations('common');
  const pathname = usePathname();
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const { user, refreshSession } = useAuth();
  const [mounted, setMounted] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [hasAdminAccess, setHasAdminAccess] = useState(false);
  const getTotalItems = useCartStore((state) => state.getTotalItems);
  const [cartCount, setCartCount] = useState(0);

  // Get locale from pathname
  const locale = pathname.split('/')[1] || 'en';

  useEffect(() => {
    setMounted(true);
    setCartCount(getTotalItems());
  }, [getTotalItems]);

  // Subscribe to cart changes
  useEffect(() => {
    const unsubscribe = useCartStore.subscribe((state) => {
      setCartCount(state.getTotalItems());
    });
    return unsubscribe;
  }, []);

  // Check admin access with session synchronization
  const checkAdminAccessWithSync = async () => {
    if (!user) {
      setHasAdminAccess(false);
      return;
    }

    try {
      // First attempt to check admin access
      const hasAccess = await checkAdminAccess();

      if (hasAccess) {
        setHasAdminAccess(true);
        return;
      }

      // If access check failed, try refreshing the session
      console.log('Admin access check failed, attempting session refresh...');
      const refreshSuccess = await refreshSession();

      if (refreshSuccess) {
        // Try checking admin access again after refresh
        const hasAccessAfterRefresh = await checkAdminAccess();
        setHasAdminAccess(hasAccessAfterRefresh);

        if (hasAccessAfterRefresh) {
          console.log('Admin access restored after session refresh');
        }
      } else {
        console.error('Session refresh failed');
        setHasAdminAccess(false);
      }
    } catch (error) {
      console.error('Error checking admin access:', error);
      setHasAdminAccess(false);
    }
  };

  // Global session synchronization for all authenticated users
  const ensureSessionSync = async () => {
    if (!user) return;

    try {
      // Check if session is still valid (doesn't require specific permissions)
      const isSessionValid = await validateSession();

      if (isSessionValid) {
        // Session is still active, no action needed
        return;
      }

      // Session might be expired, try refreshing
      console.log('Session expired, attempting refresh...');
      const refreshSuccess = await refreshSession();

      if (refreshSuccess) {
        console.log('Session refreshed successfully');
        // Re-check admin access after refresh (in case user was upgraded)
        checkAdminAccessWithSync();
      } else {
        console.error('Session refresh failed - user may need to log in again');
      }
    } catch (error) {
      console.error('Error during session sync:', error);
      // Try refreshing anyway in case of network/server errors
      try {
        const refreshSuccess = await refreshSession();
        if (refreshSuccess) {
          console.log('Session recovered after error');
          checkAdminAccessWithSync();
        }
      } catch (refreshError) {
        console.error('Session recovery failed:', refreshError);
      }
    }
  };

  // Check admin access when user changes
  useEffect(() => {
    checkAdminAccessWithSync();
  }, [user]);

  // Force recheck admin access when navigating to different pages (in case roles were updated)
  useEffect(() => {
    if (user) {
      checkAdminAccessWithSync();
    }
  }, [pathname, user]);

  // Periodic session synchronization for all authenticated users (every 45 minutes)
  useEffect(() => {
    if (!user) return;

    // Initial session check after 5 minutes of being logged in
    const initialCheckTimeout = setTimeout(() => {
      ensureSessionSync();
    }, 5 * 60 * 1000); // 5 minutes

    // Periodic checks every 45 minutes
    const syncInterval = setInterval(() => {
      ensureSessionSync();
    }, 45 * 60 * 1000); // 45 minutes

    return () => {
      clearTimeout(initialCheckTimeout);
      clearInterval(syncInterval);
    };
  }, [user]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/${locale}/products?search=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
      setMobileMenuOpen(false);
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/session', { method: 'DELETE' });
      window.location.href = `/${locale}/login`;
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const navigation = [
    { name: t('home'), href: `/${locale}`, icon: Store },
    { name: t('products'), href: `/${locale}/products`, icon: Package },
  ];

  const userMenuItems = [
    { name: t('orders'), href: `/${locale}/account/orders`, icon: Package },
    { name: t('profile'), href: `/${locale}/account/profile`, icon: User },
    { name: t('settings'), href: `/${locale}/account`, icon: Settings },
  ];

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <LoadingLink href={`/${locale}`} className="flex items-center space-x-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <Store className="h-6 w-6" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold text-foreground">MaoMao</h1>
              <p className="text-xs text-muted-foreground">Marketplace</p>
            </div>
          </LoadingLink>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              return (
                <LoadingLink
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </LoadingLink>
              );
            })}
          </nav>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-sm mx-8">
            <form onSubmit={handleSearch} className="w-full">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder={tCommon('searchProducts')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full"
                />
              </div>
            </form>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-2">
            {/* Currency Switcher */}
            <div className="hidden sm:block">
              <CurrencySwitcher />
            </div>

            {/* Theme Toggle */}
            {mounted && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className="hidden sm:flex"
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5" />
                ) : (
                  <Moon className="h-5 w-5" />
                )}
              </Button>
            )}

            {/* Cart */}
            <Link href={`/${locale}/cart`}>
              <Button variant="ghost" size="icon" className="relative">
                <ShoppingCart className="h-5 w-5" />
                {cartCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs font-medium min-w-[20px]"
                  >
                    {cartCount > 99 ? '99+' : cartCount}
                  </Badge>
                )}
              </Button>
            </Link>

            {/* User Menu */}
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="rounded-full">
                    <User className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium">{user.displayName || user.email?.split('@')[0] || 'User'}</p>
                      <p className="text-xs text-muted-foreground">{user.email}</p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {userMenuItems.map((item) => {
                    const Icon = item.icon;
                    return (
                      <DropdownMenuItem key={item.href} asChild>
                        <Link href={item.href} className="flex items-center space-x-2">
                          <Icon className="h-4 w-4" />
                          <span>{item.name}</span>
                        </Link>
                      </DropdownMenuItem>
                    );
                  })}
                  {hasAdminAccess && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/${locale}/admin`}>
                          <Settings className="h-4 w-4 mr-2" />
                          Admin Dashboard
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                    <LogOut className="h-4 w-4 mr-2" />
                    <span>{t('logout')}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="hidden sm:flex items-center space-x-2">
                <Link href={`/${locale}/login`}>
                  <Button variant="ghost" size="sm">
                    {t('login')}
                  </Button>
                </Link>
                <Link href={`/${locale}/register`}>
                  <Button size="sm">
                    {t('register')}
                  </Button>
                </Link>
              </div>
            )}

            {/* Mobile Menu */}
            <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="lg:hidden">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <SheetHeader>
                  <SheetTitle className="flex items-center space-x-3">
                    <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                      <Store className="h-5 w-5" />
                    </div>
                    <span>MaoMao Marketplace</span>
                  </SheetTitle>
                </SheetHeader>

                <div className="mt-6 space-y-6">
                  {/* Mobile Search */}
                  <form onSubmit={handleSearch} className="space-y-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="search"
                        placeholder={tCommon('searchProducts')}
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </form>

                  {/* Mobile Navigation */}
                  <nav className="space-y-2">
                    {navigation.map((item) => {
                      const Icon = item.icon;
                      const isActive = pathname === item.href;
                      return (
                        <Link
                          key={item.href}
                          href={item.href}
                          onClick={() => setMobileMenuOpen(false)}
                          className={`flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors ${
                            isActive
                              ? 'bg-primary text-primary-foreground'
                              : 'hover:bg-accent'
                          }`}
                        >
                          <Icon className="h-5 w-5" />
                          <span className="font-medium">{item.name}</span>
                        </Link>
                      );
                    })}
                  </nav>

                  {/* Mobile User Actions */}
                  {user ? (
                    <div className="space-y-4">
                      <div className="px-3 py-2 bg-muted rounded-lg">
                        <p className="font-medium">{user.displayName || user.email?.split('@')[0] || 'User'}</p>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                      </div>
                      {userMenuItems.map((item) => {
                        const Icon = item.icon;
                        return (
                          <Link
                            key={item.href}
                            href={item.href}
                            onClick={() => setMobileMenuOpen(false)}
                            className="flex items-center space-x-3 px-3 py-3 rounded-lg hover:bg-accent transition-colors"
                          >
                            <Icon className="h-5 w-5" />
                            <span>{item.name}</span>
                          </Link>
                        );
                      })}
                      {hasAdminAccess && (
                        <Link
                          href={`/${locale}/admin`}
                          onClick={() => setMobileMenuOpen(false)}
                          className="flex items-center space-x-3 px-3 py-3 rounded-lg hover:bg-accent transition-colors"
                        >
                          <Settings className="h-5 w-5" />
                          <span>Admin Dashboard</span>
                        </Link>
                      )}
                      <Button
                        variant="outline"
                        onClick={() => {
                          handleLogout();
                          setMobileMenuOpen(false);
                        }}
                        className="w-full text-red-600 border-red-200 hover:bg-red-50"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        {t('logout')}
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <Link href={`/${locale}/login`} onClick={() => setMobileMenuOpen(false)}>
                        <Button variant="outline" className="w-full">
                          {t('login')}
                        </Button>
                      </Link>
                      <Link href={`/${locale}/register`} onClick={() => setMobileMenuOpen(false)}>
                        <Button className="w-full">
                          {t('register')}
                        </Button>
                      </Link>
                    </div>
                  )}

                  {/* Mobile Settings */}
                  <div className="border-t pt-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Theme</span>
                      {mounted && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                        >
                          {theme === 'dark' ? (
                            <>
                              <Sun className="h-4 w-4 mr-2" />
                              Light
                            </>
                          ) : (
                            <>
                              <Moon className="h-4 w-4 mr-2" />
                              Dark
                            </>
                          )}
                        </Button>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Currency</span>
                      <CurrencySwitcher />
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}

