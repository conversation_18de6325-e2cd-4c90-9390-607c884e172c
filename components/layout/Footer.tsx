'use client';

// components/layout/Footer.tsx
// Enhanced footer with professional design, icons, and comprehensive sections

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import {
  MapPin,
  Phone,
  Mail,
  Facebook,
  Twitter,
  Instagram,
  Youtube,
  CreditCard,
  Shield,
  Truck,
  RotateCcw,
  HelpCircle,
  User,
  Store
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';

export function Footer() {
  const t = useTranslations('footer');
  const pathname = usePathname();
  const locale = pathname.split('/')[1] || 'en';
  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubscribing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsSubscribing(false);
    setEmail('');
    // In a real app, you'd show a success message
  };

  return (
    <footer className="bg-muted/30 mt-16 border-t">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-6">
            <div className="flex items-center space-x-2">
              <span className="text-3xl">🐱</span>
              <span className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                MaoMao
              </span>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              {t('description')}
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-sm">
                <MapPin className="h-4 w-4 text-primary flex-shrink-0" />
                <span className="text-muted-foreground">
                  {t('address')}: Shanghai, China
                </span>
              </div>
              <div className="flex items-center space-x-3 text-sm">
                <Phone className="h-4 w-4 text-primary flex-shrink-0" />
                <span className="text-muted-foreground">
                  +86 ************
                </span>
              </div>
              <div className="flex items-center space-x-3 text-sm">
                <Mail className="h-4 w-4 text-primary flex-shrink-0" />
                <span className="text-muted-foreground">
                  <EMAIL>
                </span>
              </div>
            </div>

            {/* Social Media */}
            <div className="space-y-3">
              <h4 className="text-sm font-semibold">{t('followUs')}</h4>
              <div className="flex space-x-4">
                <Link
                  href="#"
                  className="text-muted-foreground hover:text-primary transition-colors"
                  aria-label="Facebook"
                >
                  <Facebook className="h-5 w-5" />
                </Link>
                <Link
                  href="#"
                  className="text-muted-foreground hover:text-primary transition-colors"
                  aria-label="Twitter"
                >
                  <Twitter className="h-5 w-5" />
                </Link>
                <Link
                  href="#"
                  className="text-muted-foreground hover:text-primary transition-colors"
                  aria-label="Instagram"
                >
                  <Instagram className="h-5 w-5" />
                </Link>
                <Link
                  href="#"
                  className="text-muted-foreground hover:text-primary transition-colors"
                  aria-label="YouTube"
                >
                  <Youtube className="h-5 w-5" />
                </Link>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">{t('quickLinks')}</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  href={`/${locale}/products`}
                  className="text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-2"
                >
                  <span>Products</span>
                </Link>
              </li>
              <li>
                <Link
                  href={`/${locale}/account/orders`}
                  className="text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-2"
                >
                  <User className="h-4 w-4" />
                  <span>{t('myAccount')}</span>
                </Link>
              </li>
              <li>
                <Link
                  href={`/${locale}/account/orders`}
                  className="text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-2"
                >
                  <span>{t('trackOrder')}</span>
                </Link>
              </li>
              <li>
                <Link
                  href={`/${locale}/faq`}
                  className="text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-2"
                >
                  <HelpCircle className="h-4 w-4" />
                  <span>{t('faq')}</span>
                </Link>
              </li>
              <li>
                <Link
                  href={`/${locale}/contact`}
                  className="text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-2"
                >
                  <span>{t('contact')}</span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Customer Service */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">{t('customerService')}</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  href={`/${locale}/support`}
                  className="text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-2"
                >
                  <span>{t('support')}</span>
                </Link>
              </li>
              <li>
                <Link
                  href={`/${locale}/shipping`}
                  className="text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-2"
                >
                  <Truck className="h-4 w-4" />
                  <span>{t('shipping')}</span>
                </Link>
              </li>
              <li>
                <Link
                  href={`/${locale}/returns`}
                  className="text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-2"
                >
                  <RotateCcw className="h-4 w-4" />
                  <span>{t('returns')}</span>
                </Link>
              </li>
              <li>
                <Link
                  href={`/${locale}/size-guide`}
                  className="text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-2"
                >
                  <span>{t('sizeGuide')}</span>
                </Link>
              </li>
              <li>
                <Link
                  href={`/${locale}/store-locator`}
                  className="text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-2"
                >
                  <Store className="h-4 w-4" />
                  <span>{t('storeLocator')}</span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Newsletter & Legal */}
          <div className="space-y-6">
            {/* Newsletter */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">{t('newsletter')}</h3>
              <p className="text-sm text-muted-foreground">
                {t('newsletterDesc')}
              </p>
              <form onSubmit={handleSubscribe} className="space-y-3">
                <Input
                  type="email"
                  placeholder={t('emailPlaceholder')}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="bg-background"
                  required
                />
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubscribing}
                >
                  {isSubscribing ? t('subscribing') : t('subscribe')}
                </Button>
              </form>
            </div>

            {/* Payment Methods */}
            <div className="space-y-3">
              <h4 className="text-sm font-semibold">{t('paymentMethods')}</h4>
              <div className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4 text-primary" />
                <span className="text-sm text-muted-foreground">
                  {t('weAccept')} Visa, Mastercard, PayPal
                </span>
              </div>
            </div>

            {/* Certifications */}
            <div className="space-y-3">
              <h4 className="text-sm font-semibold">{t('certifications')}</h4>
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-green-600" />
                <span className="text-sm text-muted-foreground">
                  {t('secureShopping')}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Bottom Section */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
          {/* Copyright */}
          <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
            <p className="text-sm text-muted-foreground">
              {t('copyright', { year: new Date().getFullYear() })}
            </p>

            {/* Legal Links */}
            <div className="flex items-center space-x-4 text-sm">
              <Link
                href={`/${locale}/terms`}
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                {t('terms')}
              </Link>
              <span className="text-muted-foreground">•</span>
              <Link
                href={`/${locale}/privacy`}
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                {t('privacy')}
              </Link>
            </div>
          </div>

          {/* Language Selector */}
          <div className="flex items-center space-x-4">
            <div className="flex space-x-2">
              <Link
                href={`/en${pathname.slice(3)}`}
                className={`text-sm px-2 py-1 rounded transition-colors ${
                  locale === 'en'
                    ? 'bg-primary text-primary-foreground font-medium'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                EN
              </Link>
              <Link
                href={`/fr${pathname.slice(3)}`}
                className={`text-sm px-2 py-1 rounded transition-colors ${
                  locale === 'fr'
                    ? 'bg-primary text-primary-foreground font-medium'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                FR
              </Link>
              <Link
                href={`/ar${pathname.slice(3)}`}
                className={`text-sm px-2 py-1 rounded transition-colors ${
                  locale === 'ar'
                    ? 'bg-primary text-primary-foreground font-medium'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                AR
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
