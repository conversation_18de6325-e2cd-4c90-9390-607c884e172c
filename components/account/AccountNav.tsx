'use client';

// components/account/AccountNav.tsx
// Account sidebar navigation

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Home, Package, User } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AccountNavProps {
  locale: string;
}

export function AccountNav({ locale }: AccountNavProps) {
  const t = useTranslations('account');
  const pathname = usePathname();

  const navItems = [
    {
      href: `/${locale}/account`,
      label: t('dashboard'),
      icon: Home,
    },
    {
      href: `/${locale}/account/orders`,
      label: t('orders'),
      icon: Package,
    },
    {
      href: `/${locale}/account/profile`,
      label: t('profile'),
      icon: User,
    },
  ];

  return (
    <nav className="space-y-2">
      {navItems.map((item) => {
        const Icon = item.icon;
        const isActive = pathname === item.href;

        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              'flex items-center gap-3 px-4 py-3 rounded-lg transition-colors',
              isActive
                ? 'bg-primary text-primary-foreground'
                : 'hover:bg-muted'
            )}
          >
            <Icon className="h-5 w-5" />
            <span className="font-medium">{item.label}</span>
          </Link>
        );
      })}
    </nav>
  );
}

