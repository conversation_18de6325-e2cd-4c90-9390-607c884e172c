'use client';

// components/account/AddressList.tsx
// Address management component

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Plus, Trash2, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { deleteAddress } from '@/lib/actions/user.actions';
import type { Address } from '@/lib/types';

interface AddressListProps {
  addresses: Address[];
  locale: string;
}

export function AddressList({ addresses, locale }: AddressListProps) {
  const t = useTranslations('account');
  const router = useRouter();
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const handleDelete = async (addressId: string) => {
    if (!confirm(t('confirmDeleteAddress'))) {
      return;
    }

    setDeletingId(addressId);

    try {
      const result = await deleteAddress(addressId);

      if (result.success) {
        router.refresh();
      } else {
        alert(result.error || t('deleteError'));
      }
    } catch (error) {
      alert(t('deleteError'));
    } finally {
      setDeletingId(null);
    }
  };

  return (
    <div className="space-y-4">
      {addresses.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <p>{t('noAddresses')}</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {addresses.map((address) => (
            <Card key={address.id} className="p-4">
              <div className="space-y-2">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="font-semibold">{address.fullName}</p>
                    <p className="text-sm text-muted-foreground">{address.phone}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDelete(address.id)}
                    disabled={deletingId === address.id}
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>
                <div className="text-sm">
                  <p>{address.addressLine1}</p>
                  {address.addressLine2 && <p>{address.addressLine2}</p>}
                  <p>
                    {address.city}, {address.state} {address.postalCode}
                  </p>
                  <p>{address.country}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      <Button variant="outline" className="w-full">
        <Plus className="h-4 w-4 mr-2" />
        {t('addNewAddress')}
      </Button>
    </div>
  );
}

