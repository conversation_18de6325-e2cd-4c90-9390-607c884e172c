'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Share2, MessageCircle, Facebook, Send, Music, Copy, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/lib/utils';
import { MARKETPLACES } from '@/lib/constants';
import type { ProductWithDetails } from '@/lib/types';

interface SocialShareProps {
  product: ProductWithDetails;
  locale: string;
}

export function SocialShare({ product, locale }: SocialShareProps) {
  const t = useTranslations('products');
  const [copied, setCopied] = useState(false);
  const [showShareOptions, setShowShareOptions] = useState(false);

  // Get product details
  const translation = product.translations[0];
  const productName = translation?.name || product.original_name || 'Product';
  const productUrl = `${window.location.origin}/${locale}/products/${translation?.slug}`;
  const price = product.offers[0] ? Number(product.offers[0].price_low) : 0;
  const currency = product.offers[0]?.currency || 'CNY';
  const marketplace = MARKETPLACES[product.marketplace];
  const imageUrl = product.product_images[0]?.image_url;

  // Create share text
  const shareText = `Check out this amazing product: ${productName}\nPrice: ${formatCurrency(price, currency)}\nFrom: ${marketplace?.name || product.marketplace}\n\n${productUrl}`;

  // Share URLs for different platforms
  const shareUrls = {
    whatsapp: `https://wa.me/?text=${encodeURIComponent(shareText)}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(productUrl)}&quote=${encodeURIComponent(`${productName} - ${formatCurrency(price, currency)} from ${marketplace?.name || product.marketplace}`)}`,
    telegram: `https://t.me/share/url?url=${encodeURIComponent(productUrl)}&text=${encodeURIComponent(`${productName}\n${formatCurrency(price, currency)}\nFrom: ${marketplace?.name || product.marketplace}`)}`,
    tiktok: `https://www.tiktok.com/share?url=${encodeURIComponent(productUrl)}&text=${encodeURIComponent(`${productName} - Amazing product from ${marketplace?.name || product.marketplace}!`)}`,
    twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(`${productName} - ${formatCurrency(price, currency)} from ${marketplace?.name || product.marketplace}`)}&url=${encodeURIComponent(productUrl)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(productUrl)}`,
    pinterest: imageUrl ? `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(productUrl)}&media=${encodeURIComponent(imageUrl)}&description=${encodeURIComponent(`${productName} - ${formatCurrency(price, currency)}`)}` : null,
  };

  const handleShare = (platform: keyof typeof shareUrls) => {
    const url = shareUrls[platform];
    if (url) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(productUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: productName,
          text: `${productName} - ${formatCurrency(price, currency)}`,
          url: productUrl,
        });
      } catch (err) {
        console.error('Error sharing:', err);
      }
    } else {
      setShowShareOptions(!showShareOptions);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <h3 className="font-semibold">{t('share')}</h3>
        <Badge variant="secondary">
          {MARKETPLACES[product.marketplace]?.icon} {MARKETPLACES[product.marketplace]?.name}
        </Badge>
      </div>

      {/* Main share button */}
      <Button
        onClick={handleNativeShare}
        variant="outline"
        className="w-full"
      >
        <Share2 className="mr-2 h-4 w-4" />
        {t('shareProduct') || 'Share Product'}
      </Button>

      {/* Share options */}
      {showShareOptions && (
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-2 gap-2">
              {/* WhatsApp */}
              <Button
                onClick={() => handleShare('whatsapp')}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <MessageCircle className="h-4 w-4 text-green-600" />
                WhatsApp
              </Button>

              {/* Facebook */}
              <Button
                onClick={() => handleShare('facebook')}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Facebook className="h-4 w-4 text-blue-600" />
                Facebook
              </Button>

              {/* Telegram */}
              <Button
                onClick={() => handleShare('telegram')}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Send className="h-4 w-4 text-blue-500" />
                Telegram
              </Button>

              {/* TikTok */}
              <Button
                onClick={() => handleShare('tiktok')}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Music className="h-4 w-4 text-black" />
                TikTok
              </Button>

              {/* Twitter/X */}
              <Button
                onClick={() => handleShare('twitter')}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                </svg>
                X/Twitter
              </Button>

              {/* LinkedIn */}
              <Button
                onClick={() => handleShare('linkedin')}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <svg className="h-4 w-4 text-blue-700" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
                LinkedIn
              </Button>

              {/* Pinterest (only if image exists) */}
              {imageUrl && (
                <Button
                  onClick={() => handleShare('pinterest')}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <svg className="h-4 w-4 text-red-600" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.749.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001.012.017z"/>
                  </svg>
                  Pinterest
                </Button>
              )}

              {/* Copy Link */}
              <Button
                onClick={handleCopyLink}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                {copied ? (
                  <Check className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
                {copied ? 'Copied!' : 'Copy Link'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Product info for sharing */}
      <div className="text-sm text-muted-foreground">
        <p className="font-medium">{productName}</p>
        <p>{formatCurrency(price, currency)}</p>
        <p className="text-xs">{t('from')} {marketplace?.name || product.marketplace}</p>
      </div>
    </div>
  );
}