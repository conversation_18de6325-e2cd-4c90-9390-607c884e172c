'use client';

// components/products/ProductDetailClient.tsx
// Client component for product detail page

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { ShoppingCart, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { useCartStore } from '@/hooks/use-cart-store';
import { formatCurrency } from '@/lib/utils';
import { MARKETPLACES } from '@/lib/constants';
import type { ProductWithDetails } from '@/lib/types';
import { useRouter } from 'next/navigation';
import { trackProductView, trackCartAdd } from '@/lib/actions/user-activity.actions';
import { SocialShare } from './SocialShare';

interface ProductDetailClientProps {
  product: ProductWithDetails;
  locale: string;
}

export function ProductDetailClient({ product, locale }: ProductDetailClientProps) {
  const t = useTranslations('products');
  const router = useRouter();
  const addItem = useCartStore((state) => state.addItem);

  const [selectedImage, setSelectedImage] = useState(0);
  const [selectedVariant, setSelectedVariant] = useState<number | undefined>();
  const [quantity, setQuantity] = useState(1);
  const [isAdding, setIsAdding] = useState(false);

  // Track product view on component mount
  useEffect(() => {
    const trackView = async () => {
      try {
        await trackProductView(product.id, product.marketplace);
      } catch (error) {
        // Silently fail - activity tracking shouldn't break the UI
        console.error('Failed to track product view:', error);
      }
    };

    trackView();
  }, [product.id, product.marketplace]);

  // Get translations
  const translation = product.translations[0];
  const productName = translation?.name || product.original_name || 'Product';

  // Get images
  const images = product.product_images;
  const currentImage = images[selectedImage]?.image_url;

  // Get price
  const lowestOffer = product.offers[0];
  const price = lowestOffer ? Number(lowestOffer.price_low) : 0;
  const currency = lowestOffer?.currency || 'CNY';

  // Get categories
  const categories = product.categories;

  // Handle add to cart
  const handleAddToCart = async () => {
    setIsAdding(true);

    try {
      // Get variant name if selected
      let variantName = '';
      if (selectedVariant) {
        const variant = product.variants.find((v) => Number(v.id) === selectedVariant);
        const variantTranslation = variant?.translations[0];
        variantName = variantTranslation?.variant_name || variant?.original_variant_name || '';
      }

      addItem({
        productId: product.id,
        variantId: selectedVariant,
        quantity,
        productName,
        productSlug: translation?.slug || '',
        variantName,
        price,
        currency,
        imageUrl: currentImage,
      });

      // Track cart addition
      try {
        await trackCartAdd(product.id, product.marketplace);
      } catch (error) {
        console.error('Failed to track cart addition:', error);
      }

      // Show success feedback
      setTimeout(() => {
        setIsAdding(false);
      }, 1000);
    } catch (error) {
      console.error('Error adding to cart:', error);
      setIsAdding(false);
    }
  };

  // Generate structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: productName,
    description: product.product_attributes.map(attr => {
      const attrTranslation = attr.translations[0];
      const key = attrTranslation?.attr_key || attr.original_attr_key;
      const value = attrTranslation?.attr_value || attr.original_attr_value;
      return `${key}: ${value}`;
    }).join(', '),
    image: images.map(img => img.image_url),
    offers: {
      "@type": "Offer",
      price: price.toString(),
      priceCurrency: currency,
      availability: "https://schema.org/InStock",
      seller: {
        "@type": "Organization",
        name: MARKETPLACES[product.marketplace]?.name || product.marketplace
      },
      priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
    },
    category: categories.map(cat => {
      const categoryTranslation = cat.category.translations[0];
      return categoryTranslation?.name || 'Category';
    }).join(', '),
    brand: {
      "@type": "Brand",
      name: MARKETPLACES[product.marketplace]?.name || product.marketplace
    },
    // Note: Could add aggregateRating if user_activity is included in the query
  };

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Images */}
      <div className="space-y-4">
        {/* Main Image */}
        <div className="relative aspect-square overflow-hidden rounded-lg bg-muted">
          {currentImage ? (
            <Image
              src={currentImage}
              alt={productName}
              fill
              className="object-cover"
              priority
            />
          ) : (
            <div className="flex h-full items-center justify-center text-muted-foreground">
              No Image
            </div>
          )}
        </div>

        {/* Thumbnail Gallery */}
        {images.length > 1 && (
          <div className="grid grid-cols-6 gap-2">
            {images.map((image, index) => (
              <button
                key={image.id}
                onClick={() => setSelectedImage(index)}
                className={`relative aspect-square overflow-hidden rounded-md border-2 transition-colors ${
                  selectedImage === index
                    ? 'border-primary'
                    : 'border-transparent hover:border-muted-foreground'
                }`}
              >
                <Image
                  src={image.image_url}
                  alt={`${productName} ${index + 1}`}
                  fill
                  className="object-cover"
                  sizes="100px"
                />
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="space-y-6">
        {/* Categories */}
        {categories.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {categories.map((cat) => {
              const categoryTranslation = cat.category.translations[0];
              return (
                <Badge key={cat.category_id} variant="secondary">
                  {categoryTranslation?.name || 'Category'}
                </Badge>
              );
            })}
          </div>
        )}

        {/* Title */}
        <div>
          <h1 className="text-3xl font-bold mb-2">{productName}</h1>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {MARKETPLACES[product.marketplace]?.icon}{' '}
              {MARKETPLACES[product.marketplace]?.name}
            </Badge>
          </div>
        </div>

        {/* Price */}
        <div className="space-y-2">
          <div className="text-3xl font-bold text-primary">
            {formatCurrency(price, currency)}
          </div>
          {lowestOffer && Number(lowestOffer.min_quantity) > 1 && (
            <p className="text-sm text-muted-foreground">
              {t('minOrder')}: {lowestOffer.min_quantity.toString()} {t('pieces')}
            </p>
          )}
        </div>

        {/* Social Share */}
        <SocialShare product={product} locale={locale} />

        <Separator />

        {/* Variants */}
        {product.variants.length > 0 && (
          <div className="space-y-3">
            <Label htmlFor="variant-select">{t('selectVariant')}</Label>
            <select
              id="variant-select"
              value={selectedVariant?.toString() || ''}
              onChange={(e) =>
                setSelectedVariant(
                  e.target.value ? parseInt(e.target.value) : undefined
                )
              }
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <option value="">Select an option</option>
              {product.variants.map((variant) => {
                const variantTranslation = variant.translations[0];
                const variantName =
                  variantTranslation?.variant_name || variant.original_variant_name;
                return (
                  <option key={variant.id} value={variant.id.toString()}>
                    {variantName} - {formatCurrency(Number(variant.price_low), variant.currency)}
                  </option>
                );
              })}
            </select>
          </div>
        )}

        {/* Quantity */}
        <div className="space-y-3">
          <Label>{t('quantity')}</Label>
          <Input
            type="number"
            min="1"
            value={quantity}
            onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
            className="w-32"
          />
        </div>

        {/* Add to Cart */}
        <Button
          size="lg"
          className="w-full"
          onClick={handleAddToCart}
          disabled={isAdding}
        >
          {isAdding ? (
            <>
              <Check className="mr-2 h-5 w-5" />
              {t('addedToCart')}
            </>
          ) : (
            <>
              <ShoppingCart className="mr-2 h-5 w-5" />
              {t('addToCart')}
            </>
          )}
        </Button>

        <Separator />

        {/* Attributes */}
        {product.product_attributes.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold">{t('specifications')}</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              {product.product_attributes.map((attr) => {
                const attrTranslation = attr.translations[0];
                const key = attrTranslation?.attr_key || attr.original_attr_key;
                const value = attrTranslation?.attr_value || attr.original_attr_value;
                return (
                  <div key={attr.id} className="flex flex-col">
                    <span className="text-muted-foreground">{key}</span>
                    <span className="font-medium">{value}</span>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
    </>
  );
}

