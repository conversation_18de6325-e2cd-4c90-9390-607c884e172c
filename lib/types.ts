// lib/types.ts
// Global TypeScript type definitions

import type { Prisma } from '@/app/generated/prisma';

// ============================================================================
// Product Types
// ============================================================================

// Product with all relations for detail page
export type ProductWithDetails = Prisma.productsGetPayload<{
  include: {
    translations: true;
    variants: {
      include: {
        translations: true;
      };
    };
    offers: true;
    product_images: true;
    product_attributes: {
      include: {
        translations: true;
      };
    };
    custom_services: {
      include: {
        translations: true;
      };
    };
    categories: {
      include: {
        category: {
          include: {
            translations: true;
          };
        };
      };
    };
  };
}>;

// Lightweight product for listing pages (optimized with select)
export type ProductListItem = Prisma.productsGetPayload<{
  select: {
    id: true;
    original_name: true;
    marketplace: true;
    translations: {
      where: {
        language_code: string;
      };
      take: 1;
      select: {
        name: true;
        slug: true;
        language_code: true;
      };
    };
    product_images: {
      where: {
        image_type: 'preview';
      };
      take: 1;
      orderBy: {
        id: 'asc';
      };
      select: {
        image_url: true;
        image_type: true;
      };
    };
    offers: {
      orderBy: {
        price_low: 'asc';
      };
      take: 1;
      select: {
        price_low: true;
        price_high: true;
        currency: true;
        min_quantity: true;
      };
    };
    categories: {
      take: 3;
      select: {
        category: {
          select: {
            id: true;
            translations: {
              where: {
                language_code: string;
              };
              take: 1;
              select: {
                name: true;
                slug: true;
              };
            };
          };
        };
      };
    };
  };
}>;

// ============================================================================
// Order Types
// ============================================================================

export type OrderWithDetails = Prisma.ordersGetPayload<{
  include: {
    order_items: {
      include: {
        product: {
          include: {
            translations: true;
            product_images: {
              where: {
                image_type: 'preview';
              };
              take: 1;
            };
          };
        };
        variant: {
          include: {
            translations: true;
          };
        };
      };
    };
    payments: true;
  };
}>;

export type OrderListItem = Prisma.ordersGetPayload<{
  include: {
    order_items: {
      include: {
        product: {
          include: {
            translations: true;
          };
        };
      };
    };
  };
}>;

// ============================================================================
// Customer Types
// ============================================================================

export type CustomerWithRoles = Prisma.customersGetPayload<{
  include: {
    roles: {
      include: {
        role: {
          include: {
            permissions: {
              include: {
                permission: true;
              };
            };
          };
        };
      };
    };
  };
}>;

// ============================================================================
// Cart Types
// ============================================================================

export interface CartItem {
  productId: number;
  variantId?: number;
  quantity: number;
  // Cached data for display (to avoid constant DB queries)
  productName: string;
  productSlug: string;
  variantName?: string;
  price: number;
  currency: string;
  imageUrl?: string;
}

export interface CartState {
  items: CartItem[];
  addItem: (item: CartItem) => void;
  removeItem: (productId: number, variantId?: number) => void;
  updateQuantity: (productId: number, quantity: number, variantId?: number) => void;
  clearCart: () => void;
  getTotalItems: () => number;
  getTotalPrice: () => number;
}

// ============================================================================
// Filter & Search Types
// ============================================================================

export interface ProductFilters {
  search?: string;
  categoryId?: number;
  marketplace?: string;
  minPrice?: number;
  maxPrice?: number;
  attributes?: Record<string, string[]>; // { "Color": ["Red", "Blue"], "Size": ["M", "L"] }
  sortBy?: 'price_asc' | 'price_desc' | 'newest' | 'popular';
  cursor?: string; // Base64 encoded cursor for pagination
  limit?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    limit: number;
    hasMore: boolean;
    nextCursor?: string; // Base64 encoded cursor for next page
    prevCursor?: string; // Base64 encoded cursor for previous page (optional)
  };
}

// ============================================================================
// API Response Types
// ============================================================================

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// ============================================================================
// Session Types
// ============================================================================

export interface SessionUser {
  uid: string;
  email: string;
  customerId: number;
  roles?: string[];
}

// ============================================================================
// Address Type
// ============================================================================

export interface Address {
  id: string;
  fullName: string;
  phone: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
  isDefault?: boolean;
}

// ============================================================================
// Category Types
// ============================================================================

export interface CategoryWithTranslations {
  id: number;
  parent_id: number | null;
  created: Date;
  updated: Date;
  translations: {
    id: bigint;
    category_id: number;
    language_code: string;
    name: string;
    slug: string;
  }[];
  _count: {
    products: number;
  };
}

// ============================================================================
// Pagination Types
// ============================================================================

export interface PaginationInfo {
  limit: number;
  hasMore: boolean;
  nextCursor?: string; // Base64 encoded cursor for next page
  prevCursor?: string; // Base64 encoded cursor for previous page (optional)
}

// ============================================================================
// Filter Types
// ============================================================================

export interface ProductFiltersState {
  search?: string;
  categoryId?: number;
  marketplace?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'newest' | 'price_asc' | 'price_desc' | 'popular';
}

// ============================================================================
// User Profile Types
// ============================================================================

export interface UserProfile {
  id: number;
  firebaseUid: string;
  email: string;
  fullName: string;
  phone: string | null;
  addresses: Prisma.JsonValue;
  preferredCurrency: string | null;
  created: Date;
  updated: Date;
}
