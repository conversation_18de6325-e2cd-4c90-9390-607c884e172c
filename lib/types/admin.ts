// lib/types/admin.ts
// Type definitions for admin actions using Prisma-generated types

import { Prisma } from '@/app/generated/prisma';

// ============================================================================
// CUSTOMER TYPES
// ============================================================================

/**
 * Customer with roles for admin list view (optimized with select)
 */
export type CustomerWithRoles = Prisma.customersGetPayload<{
  select: {
    id: true;
    email: true;
    full_name: true;
    created: true;
    firebase_uid: true;
    roles: {
      select: {
        role: {
          select: {
            id: true;
            name: true;
          };
        };
      };
    };
    _count: {
      select: {
        orders: true;
      };
    };
  };
}>;

/**
 * Transformed customer for admin list (flattened roles)
 */
export type CustomerListItem = Omit<CustomerWithRoles, 'roles'> & {
  roles: Array<{
    id: number;
    name: string;
  }>;
};

/**
 * Customer detail with full role and permission information
 */
export type CustomerDetail = Prisma.customersGetPayload<{
  include: {
    roles: {
      include: {
        role: {
          include: {
            permissions: {
              include: {
                permission: true;
              };
            };
          };
        };
      };
    };
    orders: {
      select: {
        id: true;
        created: true;
        status: true;
        total_amount: true;
        currency: true;
      };
    };
  };
}>;

/**
 * Transformed customer detail (flattened roles and permissions)
 */
export type CustomerDetailTransformed = Omit<CustomerDetail, 'roles'> & {
  roles: Array<{
    id: number;
    name: string;
    description: string | null;
    // eslint-disable-next-line @typescript-eslint/no-empty-object-type
    permissions: Array<Prisma.PermissionGetPayload<{}>>;
  }>;
};

// ============================================================================
// ROLE TYPES
// ============================================================================

/**
 * Role with permissions
 */
export type RoleWithPermissions = Prisma.RoleGetPayload<{
  include: {
    permissions: {
      include: {
        permission: true;
      };
    };
  };
}>;

/**
 * Transformed role (flattened permissions)
 */
export type RoleTransformed = {
  id: number;
  name: string;
  description: string | null;
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  permissions: Array<Prisma.PermissionGetPayload<{}>>;
};

// ============================================================================
// PRODUCT TYPES
// ============================================================================

/**
 * Product translation for admin
 */
export type ProductTranslation = {
  id: bigint;
  product_id: number;
  language_code: string;
  name: string;
  slug: string;
  description?: string | null;
};

/**
 * Product image for admin
 */
export type ProductImage = {
  id: bigint;
  product_id: number;
  image_url: string;
  image_type: string;
  display_order?: number | null;
};

/**
 * Category translation for admin
 */
export type CategoryTranslation = {
  id: bigint;
  category_id: number;
  language_code: string;
  name: string;
  slug: string;
};

/**
 * Category with translations
 */
export type CategoryWithTranslations = {
  id: number;
  created: Date;
  updated: Date;
  parent_id: number | null;
  translations: CategoryTranslation[];
};

/**
 * Product category join with category data
 */
export type ProductCategoryJoin = {
  product_id: number;
  category_id: number;
  category: CategoryWithTranslations;
};

/**
 * Product for admin list view (optimized with select)
 */
export type ProductAdminListItem = Prisma.productsGetPayload<{
  select: {
    id: true;
    original_name: true;
    marketplace: true;
    can_show: true;
    created: true;
    translations: {
      where: {
        language_code: string;
      };
      take: 1;
      select: {
        name: true;
        slug: true;
        language_code: true;
      };
    };
    product_images: {
      take: 1;
      orderBy: {
        id: 'asc';
      };
      select: {
        image_url: true;
        image_type: true;
      };
    };
    categories: {
      take: 3;
      select: {
        category: {
          select: {
            id: true;
            translations: {
              where: {
                language_code: string;
              };
              take: 1;
              select: {
                name: true;
              };
            };
          };
        };
      };
    };
  };
}>;

/**
 * Product detail for admin
 */
export type ProductAdminDetail = Prisma.productsGetPayload<{
  include: {
    translations: true;
    product_images: true;
    categories: {
      include: {
        category: {
          include: {
            translations: true;
          };
        };
      };
    };
    variants: true;
    offers: true;
  };
}>;

// ============================================================================
// ORDER TYPES
// ============================================================================

/**
 * Order for admin list view (optimized with select)
 */
export type OrderAdminListItem = Prisma.ordersGetPayload<{
  select: {
    id: true;
    status: true;
    total_amount: true;
    currency: true;
    created: true;
    customer: {
      select: {
        id: true;
        email: true;
        full_name: true;
      };
    };
    order_items: {
      take: 3;
      select: {
        id: true;
        quantity: true;
        price_per_unit: true;
      };
    };
    _count: {
      select: {
        order_items: true;
      };
    };
  };
}>;

/**
 * Recent order for dashboard
 */
export type RecentOrder = Prisma.ordersGetPayload<{
  include: {
    customer: {
      select: {
        id: true;
        email: true;
        full_name: true;
      };
    };
  };
}>;

/**
 * Dashboard statistics
 */
export interface DashboardStats {
  totalRevenue: number;
  totalOrders: number;
  pendingOrders: number;
  processingOrders: number;
  recentOrders: RecentOrder[];
}

// ============================================================================
// PRICING TYPES
// ============================================================================

/**
 * Pricing rule type
 */
export type PricingRule = Prisma.pricing_rulesGetPayload<Record<string, never>>;

// ============================================================================
// COMMON TYPES
// ============================================================================

/**
 * Admin paginated response (cursor-based)
 */
export interface AdminPaginatedResponse<T> {
  data: T[];
  hasMore: boolean;
  nextCursor?: string; // Base64 encoded cursor for next page
  prevCursor?: string; // Base64 encoded cursor for previous page (optional)
}

/**
 * Error response
 */
export interface ErrorResponse {
  error: string;
}

/**
 * Success response
 */
export interface SuccessResponse {
  success: boolean;
  error?: string;
  message?: string;
}

/**
 * Success response with ID
 */
export interface SuccessResponseWithId extends SuccessResponse {
  ruleId?: string;
  orderId?: string;
  customerId?: string;
  productId?: string;
  receiptId?: string;
  paymentId?: string;
}

// ============================================================================
// WAREHOUSE TYPES
// ============================================================================

/**
 * Warehouse receipt with order item details
 */
export type WarehouseReceiptWithOrderItem = Prisma.warehouse_receiptsGetPayload<{
  select: {
    id: true;
    marketplace_order_id: true;
    package_weight: true;
    package_weight_unit: true;
    status: true;
    received_at: true;
    tracking_number: true;
    carrier: true;
    shipping_label_url: true;
    order_item: {
      select: {
        id: true;
        quantity: true;
        price_per_unit: true;
        order: {
          select: {
            id: true;
            status: true;
            customer: {
              select: {
                id: true;
                email: true;
                full_name: true;
              };
            };
          };
        };
        product: {
          select: {
            id: true;
            original_name: true;
            translations: {
              where: {
                language_code: string;
              };
              take: 1;
              select: {
                name: true;
                slug: true;
              };
            };
          };
        };
        variant: {
          select: {
            id: true;
            original_variant_name: true;
            translations: {
              where: {
                language_code: string;
              };
              take: 1;
              select: {
                variant_name: true;
              };
            };
          };
        };
      };
    };
  };
}>;

/**
 * Warehouse receipt list item (simplified for table view)
 */
export type WarehouseReceiptListItem = Prisma.warehouse_receiptsGetPayload<{
  select: {
    id: true;
    marketplace_order_id: true;
    package_weight: true;
    package_weight_unit: true;
    status: true;
    received_at: true;
    order_item: {
      select: {
        id: true;
        order: {
          select: {
            id: true;
            customer: {
              select: {
                full_name: true;
                email: true;
              };
            };
          };
        };
        product: {
          select: {
            original_name: true;
            translations: {
              take: 1;
              select: {
                name: true;
              };
            };
          };
        };
      };
    };
  };
}>;

/**
 * Order consolidation status
 */
export interface OrderConsolidationStatus {
  orderId: string;
  totalItems: number;
  pendingItems: number;
  matchedItems: number;
  shippedItems: number;
  canConsolidate: boolean;
  items: Array<{
    orderItemId: number;
    productName: string;
    variantName?: string;
    quantity: number;
    receiptStatus: 'none' | 'pending' | 'matched' | 'shipped';
    receiptId?: string;
    trackingNumber?: string;
  }>;
}

// ============================================================================
// PAYMENT TYPES
// ============================================================================

/**
 * Payment with order details
 */
export type PaymentWithOrder = Prisma.paymentsGetPayload<{
  select: {
    id: true;
    amount: true;
    currency: true;
    payment_method: true;
    transaction_id: true;
    status: true;
    created: true;
    order: {
      select: {
        id: true;
        status: true;
        total_amount: true;
        currency: true;
        customer: {
          select: {
            id: true;
            email: true;
            full_name: true;
          };
        };
      };
    };
  };
}>;

/**
 * Payment list item (simplified for table view)
 */
export type PaymentListItem = Prisma.paymentsGetPayload<{
  select: {
    id: true;
    amount: true;
    currency: true;
    payment_method: true;
    transaction_id: true;
    status: true;
    created: true;
    order: {
      select: {
        id: true;
        customer: {
          select: {
            full_name: true;
            email: true;
          };
        };
      };
    };
  };
}>;

