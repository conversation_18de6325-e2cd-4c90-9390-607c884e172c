// lib/actions/admin/pricing.actions.ts
// Admin pricing management server actions

'use server';

import { revalidatePath } from 'next/cache';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';
import type {
  PricingRule,
  ErrorResponse,
  SuccessResponseWithId,
  SuccessResponse,
} from '@/lib/types/admin';

/**
 * Get all pricing rules
 */
export async function getAllPricingRules(): Promise<PricingRule[] | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission - requires READ permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRICING_RULE_READ
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const rules = await prisma.pricing_rules.findMany({
      orderBy: { priority: 'asc' },
    });

    return rules;
  } catch (error) {
    console.error('Error fetching pricing rules:', error);
    return { error: 'Failed to fetch pricing rules' };
  }
}

/**
 * Create pricing rule
 */
export async function createPricingRule(data: {
  rule_name: string;
  condition_type: string;
  condition_value?: string;
  markup_type: string;
  markup_value: number;
  priority: number;
  is_active: boolean;
}): Promise<SuccessResponseWithId> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission - requires CREATE permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRICING_RULE_CREATE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    const rule = await prisma.pricing_rules.create({
      data: {
        rule_name: data.rule_name,
        condition_type: data.condition_type,
        condition_value: data.condition_value,
        markup_type: data.markup_type,
        markup_value: data.markup_value,
        priority: data.priority,
        is_active: data.is_active,
      },
    });

    revalidatePath('/admin/pricing');

    return { success: true, ruleId: rule.id.toString() };
  } catch (error) {
    console.error('Error creating pricing rule:', error);
    return { success: false, error: 'Failed to create pricing rule' };
  }
}

/**
 * Update pricing rule
 */
export async function updatePricingRule(
  ruleId: string,
  data: {
    rule_name?: string;
    condition_type?: string;
    condition_value?: string;
    markup_type?: string;
    markup_value?: number;
    priority?: number;
    is_active?: boolean;
  }
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission - requires UPDATE permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRICING_RULE_UPDATE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    await prisma.pricing_rules.update({
      where: { id: parseInt(ruleId) },
      data,
    });

    revalidatePath('/admin/pricing');

    return { success: true };
  } catch (error) {
    console.error('Error updating pricing rule:', error);
    return { success: false, error: 'Failed to update pricing rule' };
  }
}

/**
 * Delete pricing rule
 */
export async function deletePricingRule(
  ruleId: string
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission - requires DELETE permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRICING_RULE_DELETE
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    await prisma.pricing_rules.delete({
      where: { id: parseInt(ruleId) },
    });

    revalidatePath('/admin/pricing');

    return { success: true };
  } catch (error) {
    console.error('Error deleting pricing rule:', error);
    return { success: false, error: 'Failed to delete pricing rule' };
  }
}