// lib/services/exchange-rate.ts
// Exchange rate service with caching

interface ExchangeRateData {
  rates: Record<string, number>;
  base: string;
  timestamp: number;
}

class ExchangeRateService {
  private cache: Map<string, ExchangeRateData> = new Map();
  private readonly CACHE_TTL = 60 * 60 * 1000; // 1 hour in milliseconds
  private readonly API_KEY = process.env.EXCHANGE_RATE_API_KEY;
  private readonly BASE_URL = 'https://v6.exchangerate-api.com/v6';

  async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<number> {
    // Check cache first
    const cacheKey = `${fromCurrency}_${toCurrency}`;
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.rates[toCurrency] / cached.rates[fromCurrency];
    }

    // Fetch fresh data
    const data = await this.fetchExchangeRates(fromCurrency);

    // Cache the result
    this.cache.set(cacheKey, data);

    return data.rates[toCurrency] / data.rates[fromCurrency];
  }

  async getExchangeRates(baseCurrency: string = 'CNY'): Promise<Record<string, number>> {
    const cacheKey = `rates_${baseCurrency}`;
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.rates;
    }

    const data = await this.fetchExchangeRates(baseCurrency);
    this.cache.set(cacheKey, data);

    return data.rates;
  }

  private async fetchExchangeRates(baseCurrency: string): Promise<ExchangeRateData> {
    if (!this.API_KEY) {
      throw new Error('Exchange rate API key not configured');
    }

    const response = await fetch(`${this.BASE_URL}/${this.API_KEY}/latest/${baseCurrency}`);

    if (!response.ok) {
      throw new Error(`Exchange rate API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.result !== 'success') {
      throw new Error(`Exchange rate API error: ${data['error-type']}`);
    }

    return {
      rates: data.conversion_rates,
      base: data.base_code,
      timestamp: Date.now(),
    };
  }

  // Convert amount from one currency to another
  async convertCurrency(amount: number, fromCurrency: string, toCurrency: string): Promise<number> {
    if (fromCurrency === toCurrency) {
      return amount;
    }

    const rate = await this.getExchangeRate(fromCurrency, toCurrency);
    return amount * rate;
  }
}

export const exchangeRateService = new ExchangeRateService();