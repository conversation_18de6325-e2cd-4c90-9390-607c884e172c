// lib/services/geolocation.ts
// Geolocation service for detecting user country

interface GeolocationData {
  country_code: string;
  country_name: string;
  city?: string;
  region?: string;
}

class GeolocationService {
  private readonly API_URL = 'https://ipapi.co/json/';

  async getUserCountry(): Promise<string | null> {
    try {
      const response = await fetch(this.API_URL, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Geolocation API error: ${response.status}`);
      }

      const data: GeolocationData = await response.json();

      if (data.country_code) {
        return data.country_code.toUpperCase();
      }

      return null;
    } catch (error) {
      console.error('Error fetching geolocation:', error);
      return null;
    }
  }
}

export const geolocationService = new GeolocationService();