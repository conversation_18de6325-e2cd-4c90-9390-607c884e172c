// lib/constants.ts
// Application-wide constants

// Site metadata
export const SITE_CONFIG = {
  name: 'Mao<PERSON>ao',
  description: 'Your gateway to Chinese marketplaces - Taobao, Pinduoduo, and Alibaba. Specially designed for African customers with local currency support and fast shipping worldwide.',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
} as const;

// Supported locales
export const LOCALES = ['en', 'fr', 'ar'] as const;
export const DEFAULT_LOCALE = 'en' as const;

// Supported currencies
export const CURRENCIES = {
  USD: { symbol: '$', name: 'US Dollar' },
  EUR: { symbol: '€', name: 'Euro' },
  GBP: { symbol: '£', name: 'British Pound' },
  JPY: { symbol: '¥', name: 'Japanese Yen' },
  CNY: { symbol: '¥', name: 'Chinese Yuan' },
  CAD: { symbol: 'C$', name: 'Canadian Dollar' },
  AUD: { symbol: 'A$', name: 'Australian Dollar' },
  ZAR: { symbol: 'R', name: 'South African Rand' },
  NGN: { symbol: '₦', name: 'Nigerian Naira' },
  KES: { symbol: 'KSh', name: 'Kenyan Shilling' },
  GHS: { symbol: '₵', name: 'Ghanaian Cedi' },
  EGP: { symbol: 'E£', name: 'Egyptian Pound' },
  MAD: { symbol: 'MAD', name: 'Moroccan Dirham' },
  TND: { symbol: 'TND', name: 'Tunisian Dinar' },
  DZD: { symbol: 'DZD', name: 'Algerian Dinar' },
  XAF: { symbol: 'FCFA', name: 'Central African CFA Franc' },
  XOF: { symbol: 'CFA', name: 'West African CFA Franc' },
} as const;

export const DEFAULT_CURRENCY = 'USD' as const;

// Country to currency mapping for geolocation-based detection
export const COUNTRY_CURRENCY_MAP: Record<string, string> = {
  // African countries
  'ZA': 'ZAR', // South Africa
  'NG': 'NGN', // Nigeria
  'KE': 'KES', // Kenya
  'GH': 'GHS', // Ghana
  'EG': 'EGP', // Egypt
  'MA': 'MAD', // Morocco
  'TN': 'TND', // Tunisia
  'DZ': 'DZD', // Algeria
  'SN': 'XOF', // Senegal (West Africa CFA)
  'BF': 'XOF', // Burkina Faso
  'ML': 'XOF', // Mali
  'NE': 'XOF', // Niger
  'BJ': 'XOF', // Benin
  'TG': 'XOF', // Togo
  'CI': 'XOF', // Côte d'Ivoire
  'GW': 'XOF', // Guinea-Bissau
  'CM': 'XAF', // Cameroon (Central Africa CFA)
  'CF': 'XAF', // Central African Republic
  'CG': 'XAF', // Congo
  'GA': 'XAF', // Gabon
  'GQ': 'XAF', // Equatorial Guinea
  'TD': 'XAF', // Chad

  // Additional African countries (unsupported currencies default to USD)
  'LY': 'USD', // Libya (LYD unsupported)
  'SD': 'USD', // Sudan (SDG unsupported)
  'SS': 'USD', // South Sudan (SSP unsupported)
  'ET': 'USD', // Ethiopia (ETB unsupported)
  'SO': 'USD', // Somalia (SOS unsupported)
  'DJ': 'USD', // Djibouti (DJF unsupported)
  'ER': 'USD', // Eritrea (ERN unsupported)
  'UG': 'USD', // Uganda (UGX unsupported)
  'RW': 'USD', // Rwanda (RWF unsupported)
  'BI': 'USD', // Burundi (BIF unsupported)
  'TZ': 'USD', // Tanzania (TZS unsupported)
  'ZM': 'USD', // Zambia (ZMW unsupported)
  'MW': 'USD', // Malawi (MWK unsupported)
  'MZ': 'USD', // Mozambique (MZN unsupported)
  'ZW': 'USD', // Zimbabwe (ZWL unsupported)
  'BW': 'USD', // Botswana (BWP unsupported)
  'NA': 'USD', // Namibia (NAD unsupported)
  'LS': 'USD', // Lesotho (LSL unsupported)
  'SZ': 'USD', // Eswatini (SZL unsupported)
  'AO': 'USD', // Angola (AOA unsupported)
  'CD': 'USD', // Democratic Republic of Congo (CDF unsupported)
  'GN': 'USD', // Guinea (GNF unsupported)
  'GM': 'USD', // Gambia (GMD unsupported)
  'SL': 'USD', // Sierra Leone (SLL unsupported)
  'LR': 'USD', // Liberia (LRD unsupported)
  'CV': 'USD', // Cape Verde (CVE unsupported)
  'ST': 'USD', // São Tomé and Príncipe (STD unsupported)
  'MR': 'USD', // Mauritania (MRU unsupported)
  'EH': 'MAD', // Western Sahara (use Moroccan Dirham)

  // European countries
  'GB': 'GBP', // United Kingdom
  'DE': 'EUR', // Germany
  'FR': 'EUR', // France
  'IT': 'EUR', // Italy
  'ES': 'EUR', // Spain
  'NL': 'EUR', // Netherlands
  'BE': 'EUR', // Belgium
  'AT': 'EUR', // Austria
  'PT': 'EUR', // Portugal
  'FI': 'EUR', // Finland
  'IE': 'EUR', // Ireland
  'GR': 'EUR', // Greece
  'SE': 'EUR', // Sweden
  'DK': 'EUR', // Denmark
  'NO': 'EUR', // Norway (not in EU but uses EUR widely)
  'CH': 'EUR', // Switzerland (CHF but EUR is acceptable)

  // North America
  'US': 'USD', // United States
  'CA': 'USD', // Canada (CAD but USD is widely accepted)
  'MX': 'USD', // Mexico (MXN but USD is widely accepted)

  // Asia
  'JP': 'JPY', // Japan
  'CN': 'CNY', // China
  'KR': 'USD', // South Korea (KRW but USD is acceptable)
  'IN': 'USD', // India (INR but USD is acceptable)
  'SG': 'USD', // Singapore (SGD but USD is acceptable)
  'HK': 'USD', // Hong Kong (HKD but USD is acceptable)
  'TW': 'USD', // Taiwan (TWD but USD is acceptable)

  // Oceania
  'AU': 'AUD', // Australia
  'NZ': 'USD', // New Zealand (NZD but USD is acceptable)

  // South America
  'BR': 'USD', // Brazil (BRL but USD is acceptable)
  'AR': 'USD', // Argentina (ARS but USD is acceptable)
  'CL': 'USD', // Chile (CLP but USD is acceptable)
  'CO': 'USD', // Colombia (COP but USD is acceptable)
  'PE': 'USD', // Peru (PEN but USD is acceptable)

  // Default fallback
  'DEFAULT': 'USD',
} as const;

// Cursor Pagination - Default limits
export const PRODUCTS_PER_PAGE = 24;
export const ORDERS_PER_PAGE = 10;
export const MAX_PRODUCT_IMAGES_DISPLAY = 10; // Limit images shown per product

// Product filters
export const PRICE_RANGES = [
  { label: 'Under $25', min: 0, max: 25 },
  { label: '$25 - $50', min: 25, max: 50 },
  { label: '$50 - $100', min: 50, max: 100 },
  { label: '$100 - $200', min: 100, max: 200 },
  { label: 'Over $200', min: 200, max: null },
] as const;

// Marketplaces
export const MARKETPLACES = {
  taobao: { name: 'Taobao', icon: '🛍️' },
  pinduoduo: { name: 'Pinduoduo', icon: '🛒' },
  alibaba: { name: 'Alibaba', icon: '🏪' },
} as const;

// Order statuses with display info
export const ORDER_STATUS_CONFIG = {
  pending: {
    label: 'Pending Payment',
    color: 'yellow',
    description: 'Awaiting payment confirmation',
  },
  processing: {
    label: 'Processing',
    color: 'blue',
    description: 'Order is being processed',
  },
  shipped: {
    label: 'Shipped',
    color: 'purple',
    description: 'Order has been shipped',
  },
  delivered: {
    label: 'Delivered',
    color: 'green',
    description: 'Order has been delivered',
  },
  cancelled: {
    label: 'Cancelled',
    color: 'red',
    description: 'Order has been cancelled',
  },
  refunded: {
    label: 'Refunded',
    color: 'gray',
    description: 'Order has been refunded',
  },
} as const;

// Routes
export const ROUTES = {
  HOME: '/',
  PRODUCTS: '/products',
  CART: '/cart',
  LOGIN: '/login',
  REGISTER: '/register',
  ACCOUNT: '/account',
  ACCOUNT_PROFILE: '/account/profile',
  ACCOUNT_ORDERS: '/account/orders',
  CHECKOUT_SHIPPING: '/checkout/shipping',
  CHECKOUT_PAYMENT: '/checkout/payment',
  ADMIN: '/admin',
} as const;

// Protected routes (require authentication)
export const PROTECTED_ROUTES = [
  '/account',
  '/checkout',
] as const;

// Public routes (redirect to account if authenticated)
export const AUTH_ROUTES = [
  '/login',
  '/register',
] as const;

// ID token cookie name
export const ID_TOKEN_COOKIE_NAME = '__session';
export const ID_TOKEN_MAX_AGE = 60 * 60; // 1 hour in seconds

// Cart storage key
export const CART_STORAGE_KEY = 'maomao-cart';

// Image optimization
export const IMAGE_SIZES = {
  thumbnail: 150,
  card: 300,
  detail: 800,
  full: 1200,
} as const;
