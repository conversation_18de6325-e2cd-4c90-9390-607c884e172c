{"common": {"loading": "Loading...", "error": "An error occurred", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "search": "Search", "searchProducts": "Search products...", "filter": "Filter", "sort": "Sort", "clear": "Clear", "apply": "Apply", "back": "Back", "next": "Next", "previous": "Previous", "viewAll": "View All", "learnMore": "Learn More", "pageNotFound": "Page Not Found", "pageNotFoundDesc": "The page you are looking for does not exist or has been moved.", "goHome": "Go Home", "browseProducts": "Browse Products"}, "nav": {"home": "Home", "products": "Products", "cart": "<PERSON><PERSON>", "account": "My Account", "orders": "My Orders", "profile": "Profile", "settings": "Settings", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "loginTitle": "Welcome Back", "loginSubtitle": "Sign in to your account", "loggingIn": "Logging in...", "register": "Register", "registerTitle": "Create Account", "registerSubtitle": "Start shopping from Chinese marketplaces", "registering": "Creating account...", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "fullName": "Full Name", "phone": "Phone Number", "forgotPassword": "Forgot password?", "noAccount": "Don't have an account?", "haveAccount": "Already have an account?", "hasAccount": "Already have an account?", "signIn": "Sign In", "signUp": "Sign Up", "signingIn": "Signing in...", "signingUp": "Creating account...", "loginError": "Invalid email or password", "registerError": "Failed to create account", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "confirmPasswordPlaceholder": "Confirm your password", "fullNamePlaceholder": "Enter your full name", "emailRequired": "Email is required", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 6 characters", "passwordsNotMatch": "Passwords do not match", "fullNameRequired": "Full name is required", "continueWithGoogle": "Continue with Google", "or": "or"}, "products": {"title": "Products", "searchPlaceholder": "Search products...", "noResults": "No products found", "showingResults": "Showing {count} products", "sortBy": "Sort by", "sortNewest": "Newest", "sortPriceLow": "Price: Low to High", "sortPriceHigh": "Price: High to Low", "sortPopular": "Most Popular", "filters": "Filters", "priceRange": "Price Range", "marketplace": "Marketplace", "category": "Category", "allCategories": "All Categories", "clearFilters": "Clear Filters", "from": "From", "minOrder": "Min. Order", "pieces": "pieces", "addToCart": "Add to Cart", "addedToCart": "Added to <PERSON><PERSON>!", "addingToCart": "Adding...", "selectVariant": "Select options", "quantity": "Quantity", "description": "Description", "specifications": "Specifications", "shipping": "Shipping", "reviews": "Reviews", "relatedProducts": "Related Products", "outOfStock": "Out of Stock", "inStock": "In Stock", "loading": "Loading...", "previous": "Previous", "next": "Next", "share": "Share", "shareProduct": "Share Product", "seoDescription": "Browse our extensive collection of authentic Chinese products from trusted marketplaces. Find electronics, fashion, home goods and more with worldwide shipping."}, "cart": {"title": "Shopping Cart", "empty": "Your cart is empty", "continueShopping": "Continue Shopping", "item": "<PERSON><PERSON>", "price": "Price", "quantity": "Quantity", "total": "Total", "subtotal": "Subtotal", "shipping": "Shipping", "estimatedTotal": "Estimated Total", "proceedToCheckout": "Proceed to Checkout", "remove": "Remove", "update": "Update", "itemsInCart": "{count} items in cart", "addedToCart": "Added to cart successfully", "removedFromCart": "Removed from cart", "updatedCart": "Cart updated"}, "checkout": {"title": "Checkout", "shippingAddress": "Shipping Address", "selectShippingAddress": "Select your shipping address", "selectAddress": "Select an address", "selectAddressError": "Please select a shipping address", "emptyCartError": "Your cart is empty", "orderError": "Failed to place order. Please try again.", "noAddressesFound": "No addresses found", "addAddressInProfile": "Please add an address in your profile first", "addNewAddress": "Add New Address", "useThisAddress": "Use This Address", "paymentMethod": "Payment Method", "orderSummary": "Order Summary", "placeOrder": "Place Order", "processing": "Processing...", "placingOrder": "Placing order...", "orderSuccess": "Order Placed Successfully!", "orderPlaced": "Order Placed Successfully!", "orderNumber": "Order Number", "thankYou": "Thank you for your order", "paymentInstructions": "Payment Instructions", "paymentInstructionsText": "Please transfer the total amount to our bank account. Your order will be processed once we confirm your payment.", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2 (Optional)", "city": "City", "state": "State/Province", "postalCode": "Postal Code", "country": "Country", "saveAddress": "Save Address", "setAsDefault": "Set as default address", "items": "Items", "total": "Total", "subtotal": "Subtotal", "shipping": "Shipping", "bankTransferDetails": "Bank Transfer Details", "bankName": "Bank Name", "accountName": "Account Name", "accountNumber": "Account Number", "swiftCode": "SWIFT Code", "reference": "Reference", "paymentNote": "Please include your order number in the payment reference for faster processing.", "whatNext": "What happens next?", "step1Title": "Payment Confirmation", "step1Description": "We'll send you payment instructions via email", "step2Title": "Order Processing", "step2Description": "Once payment is confirmed, we'll start processing your order", "step3Title": "Shipping", "step3Description": "Your order will be shipped and you'll receive tracking information", "viewOrder": "View Order Details", "continueShopping": "Continue Shopping"}, "account": {"title": "My Account", "myAccount": "My Account", "dashboard": "Dashboard", "orders": "Orders", "orderHistory": "Order History", "profile": "Profile", "profileInformation": "Profile Information", "addresses": "Addresses", "savedAddresses": "Saved Addresses", "settings": "Settings", "welcomeBack": "Welcome back, {name}", "recentOrders": "Recent Orders", "viewAll": "View All", "viewAllOrders": "View All Orders", "noOrders": "You haven't placed any orders yet", "startShopping": "Start Shopping", "orderNumber": "Order #{id}", "orderDate": "Ordered on {date}", "orderStatus": "Status", "orderTotal": "Total", "viewOrder": "View Order", "viewDetails": "View Details", "trackOrder": "Track Order", "updateProfile": "Update Profile", "profileUpdated": "Profile updated successfully", "updateError": "Failed to update profile", "totalOrders": "Total Orders", "accountStatus": "Account Status", "active": "Active", "items": "items", "placed": "Placed", "previous": "Previous", "next": "Next", "errorLoadingOrders": "Error loading orders", "email": "Email", "emailCannotChange": "Email cannot be changed", "fullName": "Full Name", "phone": "Phone Number", "preferredCurrency": "Preferred Currency", "saving": "Saving...", "saveChanges": "Save Changes", "noAddresses": "No saved addresses", "addNewAddress": "Add New Address", "confirmDeleteAddress": "Are you sure you want to delete this address?", "deleteError": "Failed to delete address", "manageProfileDescription": "Update your personal information and preferences", "editProfile": "Edit Profile", "viewOrdersDescription": "View and track your order history", "viewOrders": "View Orders"}, "order": {"order": "Order", "status": {"pending": "Pending Payment", "processing": "Processing", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "refunded": "Refunded"}, "details": "Order Details", "orderItems": "Order Items", "items": "Order Items", "shippingAddress": "Shipping Address", "paymentStatus": "Payment Status", "trackingNumber": "Tracking Number", "awaitingPayment": "Awaiting Payment Confirmation", "backToOrders": "Back to Orders", "placed": "Placed", "quantity": "Quantity", "each": "each", "orderSummary": "Order Summary", "subtotal": "Subtotal", "shipping": "Shipping", "total": "Total", "noAddress": "No address provided", "method": "Method", "paymentReceived": "Payment Received", "notShippedYet": "Not shipped yet"}, "footer": {"about": "About Us", "contact": "Contact", "terms": "Terms of Service", "privacy": "Privacy Policy", "faq": "FAQ", "support": "Customer Support", "description": "Your trusted gateway to Chinese marketplaces - Taobao, Pinduoduo, and Alibaba", "company": "Company", "customerService": "Customer Service", "quickLinks": "Quick Links", "followUs": "Follow Us", "newsletter": "Newsletter", "newsletterDesc": "Subscribe to get special offers, free giveaways, and updates.", "emailPlaceholder": "Enter your email", "subscribe": "Subscribe", "subscribing": "Subscribing...", "socialMedia": "Social Media", "contactInfo": "Contact Info", "email": "Email", "phone": "Phone", "address": "Address", "paymentMethods": "Payment Methods", "weAccept": "We Accept", "certifications": "Certifications", "secureShopping": "Secure Shopping", "copyright": "© {year} MaoMao. All rights reserved.", "allRightsReserved": "All rights reserved", "help": "Help", "shipping": "Shipping & Delivery", "returns": "Returns & Exchanges", "sizeGuide": "Size Guide", "trackOrder": "Track Your Order", "myAccount": "My Account", "wishlist": "Wishlist", "storeLocator": "Store Locator"}, "currency": {"switcher": "<PERSON><PERSON><PERSON><PERSON>", "usd": "US Dollar", "eur": "Euro", "gbp": "British Pound", "jpy": "Japanese Yen", "cny": "Chinese Yuan", "cad": "Canadian Dollar", "aud": "Australian Dollar", "zar": "South African Rand", "ngn": "Nigerian Naira", "kes": "Kenyan Shilling", "ghs": "<PERSON><PERSON>", "egp": "Egyptian Pound", "mad": "Moroccan <PERSON><PERSON><PERSON>", "tnd": "Tunisian Dinar", "dzd": "Algerian Dinar", "xaf": "Central African CFA Franc", "xof": "West African CFA Franc"}, "pricing": {"rules": "Pricing Rules", "rulesDescription": "Pricing rules are applied in order of priority (lower number = higher priority). Rules can be based on quantity, price ranges, or apply globally.", "createRule": "Create Rule", "ruleName": "Rule Name", "conditionType": "Condition Type", "conditionValue": "Condition Value", "global": "Global", "category": "Category", "productId": "Product ID", "marketplace": "Marketplace", "markupType": "Markup Type", "percentage": "Percentage", "fixedAmountAdd": "Fixed Amount Add", "fixedAmountSet": "Fixed Amount Set", "markupValue": "Markup Value", "priority": "Priority", "active": "Active", "inactive": "Inactive", "rulesApplied": "Pricing rules are applied to product base prices before currency conversion.", "noRules": "No pricing rules found"}, "home": {"heroTitle": "Shop from China's Top Marketplaces", "heroSubtitle": "Access millions of products from Taobao, Pinduoduo, and Alibaba with ease", "shopNow": "Shop Now", "feature1Title": "Wide Selection", "feature1Description": "Access millions of products from top Chinese marketplaces", "feature2Title": "Fast Shipping", "feature2Description": "Consolidated shipping from our warehouse in China", "feature3Title": "Secure Payment", "feature3Description": "Multiple payment options with buyer protection", "feature4Title": "Global Reach", "feature4Description": "We ship worldwide with tracking", "featuredProducts": "Featured Products", "featuredProductsDescription": "Discover our handpicked selection of trending items", "shopByCategory": "Shop by Category", "shopByCategoryDescription": "Browse products by category", "viewAll": "View All", "products": "products", "seoTitle": "Chinese Marketplace for Africa - Premium Products from Taobao, Pinduoduo & Alibaba", "seoDescription": "Discover authentic Chinese products specially designed for African customers. Shop electronics, fashion, and more from trusted marketplaces like Taobao, Pinduoduo, and Alibaba with local currency support and fast worldwide shipping."}, "admin": {"title": "Admin Dashboard", "dashboard": "Dashboard", "orders": "Orders", "products": "Products", "customers": "Customers", "pricing": "Pricing", "settings": "Settings", "logout": "Logout", "welcome": "Welcome back", "overview": "Overview", "stats": {"totalRevenue": "Total Revenue", "totalOrders": "Total Orders", "pendingOrders": "Pending Orders", "processingOrders": "Processing Orders", "totalCustomers": "Total Customers", "totalProducts": "Total Products"}, "recentOrders": "Recent Orders", "viewAllOrders": "View All Orders", "orderManagement": "Order Management", "orderDetails": "Order Details", "updateStatus": "Update Status", "trackingNumber": "Tracking Number", "trackingNumberPlaceholder": "Enter tracking number", "updateOrder": "Update Order", "updating": "Updating...", "orderUpdated": "Order updated successfully", "updateError": "Failed to update order", "searchOrders": "Search orders...", "filterByStatus": "Filter by status", "allStatuses": "All Statuses", "productManagement": "Product Management", "createProduct": "Create Product", "editProduct": "Edit Product", "productDetails": "Product Details", "searchProducts": "Search products...", "customerManagement": "Customer Management", "customerDetails": "Customer Details", "searchCustomers": "Search customers...", "assignRoles": "Assign Roles", "roles": "Roles", "permissions": "Permissions", "pricingManagement": "Pricing Management", "createRule": "Create Rule", "editRule": "Edit Rule", "ruleDetails": "Rule Details", "noData": "No data available", "noOrders": "No orders found", "noProducts": "No products found", "noCustomers": "No customers found", "noRules": "No pricing rules found", "search": "Search", "view": "View", "confirmDelete": "Are you sure you want to delete this item?", "deleteError": "Failed to delete item", "actions": "Actions", "edit": "Edit", "delete": "Delete", "deleteSuccess": "Item deleted successfully", "insufficientPermissions": "You don't have permission to perform this action", "warehouse": "Warehouse", "warehouseManagement": "Warehouse Management", "createReceipt": "Create Receipt", "receipt": "Receipt", "receiptDetails": "Receipt Details", "marketplaceOrderId": "Marketplace Order ID", "packageWeight": "Package Weight", "packageWeightUnit": "Weight Unit", "packageInformation": "Package Information", "trackingInformation": "Tracking Information", "carrier": "Carrier", "consolidation": "Consolidation", "consolidationStatus": "Consolidation Status", "noReceipts": "No receipts found", "backToWarehouse": "Back to Warehouse", "received": "Received", "receivedAt": "Received At", "addTracking": "Add Tracking", "markAsShipped": "<PERSON> as Shipped", "shippingLabel": "Shipping Label", "shippingLabelUrl": "Shipping Label URL", "viewLabel": "View Label", "searchMarketplaceOrderId": "Search by marketplace order ID...", "marketplaceOrderIdHelp": "Enter the order ID from Taobao, Pinduoduo, or Alibaba", "payments": "Payments", "paymentManagement": "Payment Management", "payment": "Payment", "paymentDetails": "Payment Details", "paymentInformation": "Payment Information", "transactionId": "Transaction ID", "paymentMethod": "Payment Method", "amount": "Amount", "method": "Method", "date": "Date", "refund": "Refund", "processRefund": "Process Refund", "refundDescription": "Process a refund for this payment", "noPayments": "No payments found", "backToPayments": "Back to Payments", "newStatus": "New Status", "orderInformation": "Order Information", "orderTotal": "Order Total", "orderStatus": "Order Status", "searchOrderId": "Search by order ID...", "recordPayment": "Record Payment", "recordPaymentDescription": "Record a payment received for this order", "recording": "Recording...", "recordPaymentError": "Failed to record payment", "transactionIdRequired": "Transaction ID is required", "paymentMethodRequired": "Payment method is required", "amountRequired": "Valid amount is required", "transactionIdPlaceholder": "e.g., TXN123456789", "transactionIdHelp": "Enter the unique transaction ID from your payment processor", "selectPaymentMethod": "Select payment method", "amountHelp": "Enter the payment amount in the order currency", "paymentMethodBankTransfer": "Bank Transfer", "paymentMethodCreditCard": "Credit Card", "paymentMethodPaypal": "PayPal", "paymentMethodCash": "Cash", "paymentMethodOther": "Other", "backToOrder": "Back to Order", "paymentAlreadyExists": "Payment Already Recorded", "paymentAlreadyExistsDescription": "This order already has a payment recorded. You can view the existing payment details.", "viewExistingPayment": "View Existing Payment", "unit": "Unit", "creating": "Creating...", "processing": "Processing...", "optional": "Optional", "weight": "Weight", "status": "Status", "created": "Created", "orderId": "Order ID", "customer": "Customer", "product": "Product", "quantity": "Quantity", "orderItemDetails": "Order Item Details", "showing": "Showing", "of": "of", "cancel": "Cancel", "statusPending": "Pending", "statusMatched": "Matched", "statusShipped": "Shipped", "statusSucceeded": "Succeeded", "statusFailed": "Failed", "statusRefunded": "Refunded", "warehouseDescription": "Manage warehouse receipts and package tracking", "newReceiptDescription": "Record a new package arrival at the warehouse", "paymentsDescription": "Manage payments and transaction records", "notAvailable": "N/A", "trackingNumberExample": "e.g., 1Z999AA10123456784", "carrierPlaceholder": "e.g., DHL, FedEx, UPS", "urlPlaceholder": "https://...", "marketplaceOrderIdPlaceholder": "e.g., TB123456789", "weightPlaceholder": "0.00", "unitGrams": "g (grams)", "unitKilograms": "kg (kilograms)", "unitPounds": "lb (pounds)", "profile": "Profile", "create": "Create", "createOrder": "Create Order", "addCustomer": "Add Customer", "addProduct": "Add Product"}}