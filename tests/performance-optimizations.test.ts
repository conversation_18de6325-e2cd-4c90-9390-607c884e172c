// tests/performance-optimizations.test.ts
// Tests for the performance optimizations implemented

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { pricingService } from '@/lib/services/pricing';
import { getProducts } from '@/lib/actions/product.actions';
import { createOrder } from '@/lib/actions/order.actions';

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    products: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    pricing_rules: {
      findMany: jest.fn(),
    },
    orders: {
      create: jest.fn(),
    },
    order_items: {
      createMany: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

// Mock exchange rate service
jest.mock('@/lib/services/exchange-rate', () => ({
  exchangeRateService: {
    getExchangeRate: jest.fn().mockResolvedValue(0.14), // 1 CNY = 0.14 USD
  },
}));

describe('Performance Optimizations', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Batch Pricing Service', () => {
    it('should calculate prices in batch efficiently', async () => {
      // Mock pricing rules
      const mockRules = [
        {
          id: 1,
          rule_name: 'Global Markup',
          markup_type: 'PERCENTAGE',
          markup_value: 20,
          priority: 1,
        },
      ];

      const mockPrisma = require('@/lib/prisma').prisma;
      mockPrisma.pricing_rules.findMany.mockResolvedValue(mockRules);

      const inputs = [
        {
          costPriceCNY: 100,
          context: {
            productId: 1,
            categoryId: 1,
            marketplace: 'alibaba',
            userCurrency: 'USD',
          },
        },
        {
          costPriceCNY: 200,
          context: {
            productId: 2,
            categoryId: 1,
            marketplace: 'alibaba',
            userCurrency: 'USD',
          },
        },
      ];

      const results = await pricingService.calculatePrices(inputs);

      expect(results).toHaveLength(2);
      expect(results[0].productId).toBe(1);
      expect(results[1].productId).toBe(2);
      
      // Should have applied 20% markup and converted to USD
      expect(results[0].displayPrice).toBeCloseTo(16.8); // (100 * 1.2) * 0.14
      expect(results[1].displayPrice).toBeCloseTo(33.6); // (200 * 1.2) * 0.14

      // Should only call pricing rules once due to caching
      expect(mockPrisma.pricing_rules.findMany).toHaveBeenCalledTimes(1);
    });

    it('should cache rules for similar contexts', async () => {
      const mockRules = [
        {
          id: 1,
          rule_name: 'Global Markup',
          markup_type: 'PERCENTAGE',
          markup_value: 15,
          priority: 1,
        },
      ];

      const mockPrisma = require('@/lib/prisma').prisma;
      mockPrisma.pricing_rules.findMany.mockResolvedValue(mockRules);

      // Same context for both products
      const inputs = [
        {
          costPriceCNY: 100,
          context: {
            categoryId: 1,
            marketplace: 'alibaba',
            userCurrency: 'USD',
          },
        },
        {
          costPriceCNY: 150,
          context: {
            categoryId: 1,
            marketplace: 'alibaba',
            userCurrency: 'USD',
          },
        },
      ];

      await pricingService.calculatePrices(inputs);

      // Should only call database once due to rule caching
      expect(mockPrisma.pricing_rules.findMany).toHaveBeenCalledTimes(1);
    });
  });

  describe('Price Sorting', () => {
    it('should sort products by actual display price', async () => {
      const mockProducts = [
        {
          id: 1,
          offers: [{ price_low: 200, currency: 'CNY' }],
          categories: [{ category: { id: 1 } }],
          marketplace: 'alibaba',
        },
        {
          id: 2,
          offers: [{ price_low: 100, currency: 'CNY' }],
          categories: [{ category: { id: 1 } }],
          marketplace: 'alibaba',
        },
      ];

      const mockPrisma = require('@/lib/prisma').prisma;
      mockPrisma.products.findMany.mockResolvedValue([...mockProducts, null]); // +1 for hasMore check
      mockPrisma.pricing_rules.findMany.mockResolvedValue([]);

      const result = await getProducts(
        { sortBy: 'price_asc', limit: 2 },
        'en'
      );

      expect(result.data).toHaveLength(2);
      // Should be sorted by price ascending (product 2 first, then product 1)
      expect(result.data[0].id).toBe(2);
      expect(result.data[1].id).toBe(1);
    });
  });
});

describe('Order Creation Optimization', () => {
  it('should pre-fetch product details to avoid N+1 queries', async () => {
    const mockUser = { customerId: 1 };
    const mockItems = [
      { productId: 1, quantity: 2, price: 100 },
      { productId: 2, quantity: 1, price: 200 },
    ];

    const mockProducts = [
      { id: 1, product_url: 'url1', marketplace: 'alibaba' },
      { id: 2, product_url: 'url2', marketplace: 'taobao' },
    ];

    const mockPrisma = require('@/lib/prisma').prisma;
    mockPrisma.products.findMany.mockResolvedValue(mockProducts);
    mockPrisma.$transaction.mockImplementation(async (callback) => {
      const mockTx = {
        orders: {
          create: jest.fn().mockResolvedValue({ id: 'order-123' }),
        },
        order_items: {
          createMany: jest.fn().mockResolvedValue({ count: 2 }),
        },
      };
      return callback(mockTx);
    });

    // Mock getCurrentUser
    jest.doMock('@/lib/auth', () => ({
      getCurrentUser: jest.fn().mockResolvedValue(mockUser),
    }));

    const result = await createOrder({
      items: mockItems,
      shippingAddress: { street: '123 Main St' },
      currency: 'USD',
      exchangeRate: 0.14,
      shippingCost: 10,
    });

    expect(result.success).toBe(true);
    
    // Should pre-fetch all products in one query
    expect(mockPrisma.products.findMany).toHaveBeenCalledTimes(1);
    expect(mockPrisma.products.findMany).toHaveBeenCalledWith({
      where: { id: { in: [1, 2] } },
      select: { id: true, product_url: true, marketplace: true },
    });

    // Should use batch createMany instead of individual creates
    const mockTx = mockPrisma.$transaction.mock.calls[0][0];
    await mockTx({
      orders: { create: jest.fn().mockResolvedValue({ id: 'order-123' }) },
      order_items: { createMany: jest.fn() },
    });
  });
});
