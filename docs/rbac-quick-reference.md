# RBAC Quick Reference Guide

## 🚀 Quick Start

### Setup (One-time)

```bash
# 1. Generate Prisma client
npx prisma generate

# 2. Push schema to database
npx prisma db push

# 3. Seed permissions and roles
npx prisma db seed
```

---

## 📋 Permission Categories

### Dashboard
- `ACCESS_ADMIN_DASHBOARD` - Access admin interface
- `VIEW_DASHBOARD_STATS` - View statistics

### Products
- `PRODUCT_CREATE` - Create products
- `PRODUCT_READ` - View products
- `PRODUCT_UPDATE` - Edit products
- `PRODUCT_DELETE` - Delete products
- `PRODUCT_MANAGE_CATEGORIES` - Manage categories
- `PRODUCT_MANAGE_VARIANTS` - Manage variants
- `PRODUCT_MANAGE_IMAGES` - Manage images

### Orders
- `ORDER_READ_ALL` - View all orders
- `ORDER_READ_OWN` - View own orders
- `ORDER_UPDATE_STATUS` - Update status
- `ORDER_CANCEL` - Cancel orders
- `ORDER_REFUND` - Process refunds
- `ORDER_DELETE` - Delete orders

### Customers
- `CUSTOMER_READ` - View customers
- `CUSTOMER_UPDATE` - Edit customers
- `CUSTOMER_DELETE` - Delete customers
- `CUSTOMER_ASSIGN_ROLE` - Assign roles
- `CUSTOMER_REMOVE_ROLE` - Remove roles
- `CUSTOMER_VIEW_SENSITIVE_DATA` - View sensitive data

### Pricing
- `PRICING_RULE_CREATE` - Create rules
- `PRICING_RULE_READ` - View rules
- `PRICING_RULE_UPDATE` - Edit rules
- `PRICING_RULE_DELETE` - Delete rules

### Roles
- `ROLE_CREATE` - Create roles
- `ROLE_READ` - View roles
- `ROLE_UPDATE` - Edit roles
- `ROLE_DELETE` - Delete roles
- `PERMISSION_ASSIGN` - Assign permissions

### Analytics
- `ANALYTICS_VIEW` - View analytics
- `REPORTS_GENERATE` - Generate reports
- `REPORTS_EXPORT` - Export reports

### Settings
- `SETTINGS_VIEW` - View settings
- `SETTINGS_UPDATE` - Update settings

---

## 👥 Predefined Roles

| Role | Permissions | Use Case |
|------|-------------|----------|
| **Super Admin** | All (64) | System owner |
| **Admin** | 24 | Business manager |
| **Product Manager** | 12 | Catalog manager |
| **Order Manager** | 10 | Fulfillment manager |
| **Customer Support** | 6 | Support agent |
| **Pricing Manager** | 9 | Pricing strategist |
| **Analyst** | 9 | Data analyst |
| **Viewer** | 6 | Read-only observer |

---

## 💻 Code Examples

### Check Permission in Server Action

```typescript
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';

export async function myServerAction() {
  // 1. Get current user
  const user = await getCurrentUser();
  
  if (!user) {
    return { error: 'Not authenticated' };
  }

  // 2. Check permission
  const hasPermission = await checkPermission(
    user.uid,
    PermissionAction.PRODUCT_CREATE
  );

  if (!hasPermission) {
    return { error: 'Insufficient permissions' };
  }

  // 3. Proceed with action
  // ... your code here
}
```

### Conditional UI Rendering (Server Component)

```typescript
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';

export default async function MyPage() {
  const user = await getCurrentUser();
  const canCreate = user 
    ? await checkPermission(user.uid, PermissionAction.PRODUCT_CREATE) 
    : false;

  return (
    <div>
      {canCreate && (
        <Button>Create Product</Button>
      )}
    </div>
  );
}
```

### Multiple Permission Checks

```typescript
const user = await getCurrentUser();

if (!user) {
  return { error: 'Not authenticated' };
}

const [canRead, canUpdate, canDelete] = await Promise.all([
  checkPermission(user.uid, PermissionAction.PRODUCT_READ),
  checkPermission(user.uid, PermissionAction.PRODUCT_UPDATE),
  checkPermission(user.uid, PermissionAction.PRODUCT_DELETE),
]);

return {
  canRead,
  canUpdate,
  canDelete,
};
```

---

## 🔧 Common Tasks

### Assign Role to User

```typescript
// Via Prisma
await prisma.customer_roles.create({
  data: {
    customer_id: 'customer-id',
    role_id: 'role-id',
  },
});

// Via Admin Dashboard
// 1. Go to /admin/customers
// 2. Click on customer
// 3. Use "Assign Roles" section
```

### Create Custom Role

```typescript
await prisma.role.create({
  data: {
    name: 'Custom Role',
    description: 'Custom role description',
    permissions: {
      connect: [
        { action: PermissionAction.PRODUCT_READ },
        { action: PermissionAction.ORDER_READ_ALL },
      ],
    },
  },
});
```

### Get User Permissions

```typescript
import { getUserPermissions } from '@/lib/auth/permissions';

const permissions = await getUserPermissions(firebaseUid);
// Returns: [PermissionAction.PRODUCT_READ, PermissionAction.ORDER_READ_ALL, ...]
```

### Check if User is Admin

```typescript
import { isAdmin } from '@/lib/auth/permissions';

const userIsAdmin = await isAdmin(firebaseUid);
// Returns: true or false
```

---

## 🛡️ Security Best Practices

### ✅ DO

1. **Always check permissions in server actions** (most critical)
2. **Use specific permissions** (e.g., `PRODUCT_DELETE` not just `ACCESS_ADMIN_DASHBOARD`)
3. **Check permissions at multiple layers** (middleware, layout, page, action)
4. **Hide UI elements** users can't access
5. **Log permission denials** for security auditing
6. **Follow principle of least privilege**

### ❌ DON'T

1. **Don't skip permission checks** in server actions
2. **Don't rely only on UI hiding** (always check on server)
3. **Don't use `ACCESS_ADMIN_DASHBOARD` for everything**
4. **Don't give users more permissions than needed**
5. **Don't forget to revalidate paths** after permission changes

---

## 🐛 Troubleshooting

### User can't access admin dashboard

1. Check if user has `ACCESS_ADMIN_DASHBOARD` permission
2. Verify user has at least one role assigned
3. Check if role has the permission
4. Clear browser cookies and re-login

### Permission check always returns false

1. Verify Prisma client is generated: `npx prisma generate`
2. Check if permissions are seeded: `npx prisma db seed`
3. Verify user's Firebase UID matches database record
4. Check database connection

### Build fails with Prisma error

1. Regenerate Prisma client: `npx prisma generate`
2. Check if schema is valid: `npx prisma validate`
3. Ensure database is accessible

---

## 📊 Permission Hierarchy

```
Super Admin (All 64 permissions)
    ↓
Admin (24 permissions)
    ↓
Product Manager (12) | Order Manager (10) | Pricing Manager (9) | Analyst (9)
    ↓
Customer Support (6) | Viewer (6)
    ↓
Regular User (0 admin permissions)
```

---

## 🔗 Related Files

- **Schema**: `prisma/schema.prisma`
- **Seed**: `prisma/seed.ts`
- **Permission Utils**: `lib/auth/permissions.ts`
- **Middleware**: `middleware.ts`
- **Admin Layout**: `app/[locale]/admin/layout.tsx`
- **Full Docs**: `docs/rbac-system.md`

---

## 📞 Need Help?

1. Read full documentation: `docs/rbac-system.md`
2. Check implementation summary: `RBAC_IMPLEMENTATION_SUMMARY.md`
3. Review code examples in server actions: `lib/actions/admin/*.ts`

---

## 🎯 Quick Commands

```bash
# Generate Prisma client
npx prisma generate

# Push schema changes
npx prisma db push

# Seed database
npx prisma db seed

# Open Prisma Studio
npx prisma studio

# Build application
npm run build

# Run development server
npm run dev
```

---

**Remember**: Always check permissions in server actions! This is your last line of defense. 🔒

