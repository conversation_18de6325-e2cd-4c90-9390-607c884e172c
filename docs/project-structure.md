### The Complete Application File Structure

```
.
├── prisma/
│   ├── schema.prisma             # The single source of truth for your database schema (with RBAC models)
│   ├── migrations/               # History of all database schema changes
│   └── seed.ts                   # Script to seed the DB with default Roles and Permissions
│
├── public/
│   ├── images/
│   └── *.svg
│
├── messages/
│   ├── en.json                   # English translation strings for next-intl
│   ├── fr.json                   # French translation strings for next-intl
│   └── ar.json                   # Arabic translation strings for next-intl
│
├── src/
│   ├── app/
│   │   ├── [locale]/             # DYNAMIC ROUTE for internationalization (i18n)
│   │   │   ├── (marketing)/
│   │   │   │   ├── products/
│   │   │   │   │   ├── page.tsx
│   │   │   │   │   └── [slug]/
│   │   │   │   │       ├── page.tsx
│   │   │   │   │       └── loading.tsx
│   │   │   │   ├── cart/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── page.tsx      # Homepage 
│   │   │   │
│   │   │   ├── (auth)/
│   │   │   │   ├── login/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── register/
│   │   │   │   │   └── page.tsx
│   │   │   │   └──layout.tsx
│   │   │   │
│   │   │   ├── account/
│   │   │   │   ├── layout.tsx
│   │   │   │   ├── profile/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── orders/
│   │   │   │   │   ├── page.tsx
│   │   │   │   │   └── [orderId]/
│   │   │   │   │       └── page.tsx
│   │   │   │   └── page.tsx
│   │   │   │
│   │   │   ├── (checkout)/
│   │   │   │   ├── layout.tsx
│   │   │   │   ├── shipping/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── payment/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── success/[orderId]/
│   │   │   │       └── page.tsx
│   │   │   │
│   │   │   ├── admin/            # ROOT of the entire admin dashboard
│   │   │   │   ├── layout.tsx
│   │   │   │   ├── page.tsx
│   │   │   │   ├── orders/
│   │   │   │   │   ├── page.tsx
│   │   │   │   │   └── [orderId]/
│   │   │   │   │       └── page.tsx
│   │   │   │   ├── products/
│   │   │   │   │   ├── page.tsx
│   │   │   │   │   ├── new/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   └── [productId]/
│   │   │   │   │       └── page.tsx
│   │   │   │   ├── customers/
│   │   │   │   │   ├── page.tsx
│   │   │   │   │   └── [customerId]/
│   │   │   │   │       └── page.tsx
│   │   │   │   └── pricing/
│   │   │   │       └── page.tsx
│   │   │   │
│   │   │   └── layout.tsx        # Root layout for the current locale
│   │   │
│   │   ├── api/
│   │   │   └── auth/
│   │   │       └── session/
│   │   │           └── route.ts  # Handles creating/destroying the Firebase session cookie
│   │   │
│   │   ├── globals.css
│   │   ├── not-found.tsx
│   │   └── layout.tsx            # The absolute root layout (renders <html> and <body>)
│   │
│   ├── components/
│   │   ├── ui/                   # Shadcn/ui components
│   │   ├── icons/
│   │   ├── layout/               # Shared layout: Navbar, Footer
│   │   ├── providers/            # Central hub for all client-side providers
│   │   │   ├── ThemeProvider.tsx
│   │   │   ├── FirebaseAuthProvider.tsx
│   │   │   └── index.tsx         # Composes all providers together for a clean root layout
│   │   ├── auth/                 # LoginForm, RegisterForm
│   │   ├── products/             # ProductCard, PriceDisplay, etc.
│   │   ├── cart/                 # CartItem, CartSummary, AddToCartButton
│   │   ├── checkout/             # ShippingForm, OrderSummary, PaymentInstructions
│   │   └── admin/                # ADMIN-SPECIFIC components (StatCard, OrderTable, etc.)
│   │
│   ├── lib/
│   │   ├── actions/
│   │   │   ├── auth.actions.ts   # createNewCustomer (called after Firebase signup)
│   │   │   ├── user.actions.ts   # updateProfile
│   │   │   ├── order.actions.ts  # createOrder
│   │   │   └── admin/            # Privileged, RBAC-protected server actions
│   │   │       ├── order.actions.ts
│   │   │       ├── product.actions.ts
│   │   │       ├── customer.actions.ts
│   │   │       └── pricing.actions.ts
│   │   │
│   │   ├── auth/
│   │   │   └── permissions.ts    # The checkPermission() function for RBAC
│   │   │
│   │   ├── firebase/
│   │   │   ├── client.ts         # Initializes and exports the Firebase CLIENT SDK
│   │   │   └── server.ts         # Initializes and exports the Firebase ADMIN SDK
│   │   │
│   │   ├── prisma.ts             # Prisma Client singleton instance
│   │   ├── utils.ts              # Helper functions (cn for Tailwind, formatCurrency)
│   │   ├── types.ts              # Global TypeScript definitions
│   │   └── constants.ts          # App-wide constants (routes, site metadata)
│   │
│   ├── hooks/
│   │   └── use-cart-store.ts     # Zustand store for the client-side shopping cart
│   │
│   ├── i18n.ts                   # Configuration for next-intl
│   └── middleware.ts             # Single middleware for locale handling and route protection
│
├── .env.local                      # All secret keys (DB_URL, Firebase Admin & Client)
├── next.config.ts
└── tsconfig.json
```

---

### Key Architectural & File Descriptions

#### Core Configuration & Setup

*   **`i18n.ts` & `messages/`**: Configures `next-intl` for internationalization. It defines the supported locales (e.g., 'en', 'zh') and where to find the translation files.
*   **`app/[locale]/`**: This is the most significant structural change. All user-visible pages now live inside this dynamic route, making internationalized URLs the default (e.g., `/en/products`, `/zh/products`).
*   **`middleware.ts`**: A unified middleware with two primary responsibilities:
    1.  **Locale Handling**: It detects the user's preferred language from headers or a cookie and redirects them to the correct locale path (e.g., `/` -> `/en`).
    2.  **Route Protection**: It verifies the `__session` cookie using the **Firebase Admin SDK** for all protected routes (`/account`, `/admin`) and redirects if the user is unauthenticated or lacks the necessary permissions.

#### Authentication Flow (Firebase)

*   **`lib/firebase/client.ts`**: Initializes the Firebase SDK for the browser using the public `NEXT_PUBLIC_` environment variables. Used by login/register forms.
*   **`lib/firebase/server.ts`**: Initializes the Firebase **Admin** SDK for the server using secret environment variables. Used by the middleware and API routes for verification.
*   **`components/providers/FirebaseAuthProvider.tsx`**: A client-side component that listens to Firebase's auth state changes (`onAuthStateChanged`) and makes the current user's state available to other client components via React Context.
*   **`app/api/auth/session/route.ts`**: The bridge between client and server.
    *   **`POST`**: Receives an ID Token from a client who just logged in, verifies it, creates a long-lived `__session` cookie, and sets it in the browser.
    *   **`DELETE`**: Clears the `__session` cookie to log the user out on the server side.
*   **`lib/actions/auth.actions.ts`**: Contains the `createNewCustomer` Server Action. After a user signs up with Firebase on the client, this action is called to create a corresponding user record in your Prisma database with their `firebase_uid`.

#### Role-Based Access Control (RBAC)

*   **`prisma/seed.ts`**: An essential script to populate your database with the default `Permission` and `Role` records.
*   **`lib/auth/permissions.ts`**: The security core of the admin app. Contains the `checkPermission()` helper function, which takes a `firebase_uid` and a required `PermissionAction` to determine if a user is authorized. This is used extensively in the admin section.

#### Admin vs. User-Facing Code

*   **`app/[locale]/admin/`**: All admin-related pages are neatly co-located here. The `layout.tsx` inside this folder is the definitive security gate, using `checkPermission()` to block any unauthorized access before rendering.
*   **`components/admin/`**: Contains UI components used exclusively in the admin dashboard (e.g., `StatCard`, `OrderTable`). This keeps them separate from the e-commerce UI.
*   **`lib/actions/admin/`**: Contains all privileged Server Actions. Every function inside this folder **must** begin with a `checkPermission()` call to ensure the user is authorized to perform that specific action (e.g., `updateOrderStatus` checks for `ORDER_UPDATE_STATUS`). This layered security is critical.