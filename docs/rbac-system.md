# Role-Based Access Control (RBAC) System

## Overview

The MaoMao e-commerce platform implements a comprehensive Role-Based Access Control (RBAC) system to manage user permissions and access to admin features. This document describes the permission structure, available roles, and how to use the system.

---

## Permission Structure

### Permission Categories

#### 1. **Admin Dashboard Access**
- `ACCESS_ADMIN_DASHBOARD` - Can access the admin dashboard interface
- `VIEW_DASHBOARD_STATS` - Can view dashboard statistics and metrics

#### 2. **Product Permissions**
- `PRODUCT_CREATE` - Can create new products
- `PRODUCT_READ` - Can view product details in admin
- `PRODUCT_UPDATE` - Can edit existing products
- `PRODUCT_DELETE` - Can delete products
- `PRODUCT_MANAGE_CATEGORIES` - Can create, edit, and delete product categories
- `PRODUCT_MANAGE_VARIANTS` - Can manage product variants and attributes
- `PRODUCT_MANAGE_IMAGES` - Can upload and manage product images

#### 3. **Order Permissions**
- `ORDER_READ_ALL` - Can view all customer orders
- `ORDER_READ_OWN` - Can view only own orders
- `ORDER_UPDATE_STATUS` - Can update order status and tracking
- `ORDER_CANCEL` - Can cancel orders
- `ORDER_REFUND` - Can process order refunds
- `ORDER_DELETE` - Can delete orders (dangerous operation)

#### 4. **Customer Permissions**
- `CUSTOMER_READ` - Can view customer information
- `CUSTOMER_UPDATE` - Can edit customer information
- `CUSTOMER_DELETE` - Can delete customer accounts
- `CUSTOMER_ASSIGN_ROLE` - Can assign roles to customers
- `CUSTOMER_REMOVE_ROLE` - Can remove roles from customers
- `CUSTOMER_VIEW_SENSITIVE_DATA` - Can view sensitive customer information

#### 5. **Pricing Rule Permissions**
- `PRICING_RULE_CREATE` - Can create new pricing rules
- `PRICING_RULE_READ` - Can view pricing rules
- `PRICING_RULE_UPDATE` - Can edit pricing rules
- `PRICING_RULE_DELETE` - Can delete pricing rules

#### 6. **Role & Permission Management**
- `ROLE_CREATE` - Can create new roles
- `ROLE_READ` - Can view roles and their permissions
- `ROLE_UPDATE` - Can edit roles and their permissions
- `ROLE_DELETE` - Can delete roles
- `PERMISSION_ASSIGN` - Can assign permissions to roles

#### 7. **Analytics & Reports**
- `ANALYTICS_VIEW` - Can view analytics and insights
- `REPORTS_GENERATE` - Can generate business reports
- `REPORTS_EXPORT` - Can export reports to various formats

#### 8. **System Settings**
- `SETTINGS_VIEW` - Can view system settings
- `SETTINGS_UPDATE` - Can modify system settings

---

## Predefined Roles

### 1. **Super Admin** 👑
**Description**: Has complete access to all system features and settings. Can manage everything.

**Permissions**: ALL (64 permissions)

**Use Case**: System owner, technical administrator

---

### 2. **Admin** 🔧
**Description**: Can manage products, orders, customers, and pricing. Cannot manage roles or system settings.

**Permissions**:
- Dashboard: `ACCESS_ADMIN_DASHBOARD`, `VIEW_DASHBOARD_STATS`
- Products: All product permissions (7)
- Orders: `ORDER_READ_ALL`, `ORDER_UPDATE_STATUS`, `ORDER_CANCEL`, `ORDER_REFUND`
- Customers: `CUSTOMER_READ`, `CUSTOMER_UPDATE`, `CUSTOMER_VIEW_SENSITIVE_DATA`
- Pricing: All pricing permissions (4)
- Analytics: `ANALYTICS_VIEW`, `REPORTS_GENERATE`, `REPORTS_EXPORT`

**Use Case**: General administrator, business manager

---

### 3. **Product Manager** 📦
**Description**: Can manage products, categories, variants, and images. Read-only access to orders.

**Permissions**:
- Dashboard: `ACCESS_ADMIN_DASHBOARD`, `VIEW_DASHBOARD_STATS`
- Products: All product permissions (7)
- Orders: `ORDER_READ_ALL` (read-only)
- Customers: `CUSTOMER_READ` (read-only)
- Analytics: `ANALYTICS_VIEW`

**Use Case**: Product catalog manager, inventory manager

---

### 4. **Order Manager** 📋
**Description**: Can manage orders, update status, process refunds. Read-only access to products and customers.

**Permissions**:
- Dashboard: `ACCESS_ADMIN_DASHBOARD`, `VIEW_DASHBOARD_STATS`
- Orders: `ORDER_READ_ALL`, `ORDER_UPDATE_STATUS`, `ORDER_CANCEL`, `ORDER_REFUND`
- Products: `PRODUCT_READ` (read-only)
- Customers: `CUSTOMER_READ`, `CUSTOMER_VIEW_SENSITIVE_DATA`
- Analytics: `ANALYTICS_VIEW`

**Use Case**: Order fulfillment manager, logistics coordinator

---

### 5. **Customer Support** 💬
**Description**: Can view orders and customers, update order status. Limited product access.

**Permissions**:
- Dashboard: `ACCESS_ADMIN_DASHBOARD`
- Orders: `ORDER_READ_ALL`, `ORDER_UPDATE_STATUS`
- Customers: `CUSTOMER_READ`, `CUSTOMER_VIEW_SENSITIVE_DATA`
- Products: `PRODUCT_READ` (read-only)

**Use Case**: Customer service representative, support agent

---

### 6. **Pricing Manager** 💰
**Description**: Can manage pricing rules and view products. Read-only access to orders.

**Permissions**:
- Dashboard: `ACCESS_ADMIN_DASHBOARD`, `VIEW_DASHBOARD_STATS`
- Pricing: All pricing permissions (4)
- Products: `PRODUCT_READ` (read-only)
- Orders: `ORDER_READ_ALL` (read-only)
- Analytics: `ANALYTICS_VIEW`

**Use Case**: Pricing strategist, revenue manager

---

### 7. **Analyst** 📊
**Description**: Can view analytics, generate reports. Read-only access to all data.

**Permissions**:
- Dashboard: `ACCESS_ADMIN_DASHBOARD`, `VIEW_DASHBOARD_STATS`
- Products: `PRODUCT_READ`
- Orders: `ORDER_READ_ALL`
- Customers: `CUSTOMER_READ`
- Pricing: `PRICING_RULE_READ`
- Analytics: `ANALYTICS_VIEW`, `REPORTS_GENERATE`, `REPORTS_EXPORT`

**Use Case**: Business analyst, data analyst

---

### 8. **Viewer** 👁️
**Description**: Read-only access to admin dashboard. Cannot modify anything.

**Permissions**:
- Dashboard: `ACCESS_ADMIN_DASHBOARD`, `VIEW_DASHBOARD_STATS`
- Products: `PRODUCT_READ`
- Orders: `ORDER_READ_ALL`
- Customers: `CUSTOMER_READ`
- Pricing: `PRICING_RULE_READ`

**Use Case**: Stakeholder, auditor, read-only observer

---

## Security Implementation

### Multi-Layer Protection

The RBAC system implements **4 layers of security**:

1. **Middleware Layer** (`middleware.ts`)
   - Checks `ACCESS_ADMIN_DASHBOARD` permission
   - Blocks unauthorized users before they reach admin routes
   - Redirects to login or account page

2. **Layout Layer** (`app/[locale]/admin/layout.tsx`)
   - Secondary permission check for `ACCESS_ADMIN_DASHBOARD`
   - Ensures only authorized users can see admin UI

3. **Page Component Layer**
   - Individual pages check specific permissions
   - Example: Product page checks `PRODUCT_READ` permission
   - Conditional rendering based on permissions (e.g., "Create" button)

4. **Server Action Layer** (`lib/actions/admin/*.ts`)
   - **Final and most critical layer**
   - Every server action checks required permission before execution
   - Example: `deleteProduct()` checks `PRODUCT_DELETE` permission

### Permission Checking

```typescript
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';

// Check if user has a specific permission
const hasPermission = await checkPermission(
  firebaseUid,
  PermissionAction.PRODUCT_CREATE
);

if (!hasPermission) {
  return { error: 'Insufficient permissions' };
}
```

---

## Database Setup

### 1. Generate Prisma Client

```bash
npx prisma generate
```

### 2. Run Seed Script

```bash
npx prisma db seed
```

This will create:
- 64 permissions
- 9 predefined roles with appropriate permissions

### 3. Assign Roles to Users

Use the admin dashboard to assign roles to users:
1. Navigate to `/admin/customers`
2. Click on a customer
3. Use the "Assign Roles" section to add/remove roles

---

## Usage Examples

### Example 1: Checking Permission in Server Action

```typescript
export async function updateProduct(productId: string, data: any) {
  const user = await getCurrentUser();
  
  if (!user) {
    return { error: 'Not authenticated' };
  }

  // Check permission
  const hasPermission = await checkPermission(
    user.uid,
    PermissionAction.PRODUCT_UPDATE
  );

  if (!hasPermission) {
    return { error: 'Insufficient permissions' };
  }

  // Proceed with update...
}
```

### Example 2: Conditional UI Rendering

```typescript
// In a Server Component
const user = await getCurrentUser();
const canCreate = user 
  ? await checkPermission(user.uid, PermissionAction.PRODUCT_CREATE) 
  : false;

return (
  <div>
    {canCreate && (
      <Button>Create Product</Button>
    )}
  </div>
);
```

---

## Best Practices

1. **Always check permissions in server actions** - This is the most critical layer
2. **Use specific permissions** - Don't rely only on `ACCESS_ADMIN_DASHBOARD`
3. **Implement conditional UI** - Hide actions users can't perform
4. **Log permission denials** - For security auditing
5. **Regularly review role assignments** - Ensure users have appropriate access
6. **Follow principle of least privilege** - Give users only the permissions they need

---

## Extending the System

### Adding New Permissions

1. Add to `prisma/schema.prisma`:
```prisma
enum PermissionAction {
  // ... existing permissions
  NEW_FEATURE_CREATE
  NEW_FEATURE_READ
  NEW_FEATURE_UPDATE
  NEW_FEATURE_DELETE
}
```

2. Update `prisma/seed.ts`:
```typescript
const allPermissions = [
  // ... existing permissions
  { 
    action: PermissionAction.NEW_FEATURE_CREATE, 
    name: 'Create New Feature', 
    description: 'Can create new feature items' 
  },
];
```

3. Regenerate Prisma client:
```bash
npx prisma generate
```

4. Run seed script:
```bash
npx prisma db seed
```

### Creating Custom Roles

You can create custom roles programmatically or through the admin interface (when implemented):

```typescript
await prisma.role.create({
  data: {
    name: 'Custom Role',
    description: 'Custom role description',
    permissions: {
      connect: [
        { action: PermissionAction.PRODUCT_READ },
        { action: PermissionAction.ORDER_READ_ALL },
      ],
    },
  },
});
```

---

## Summary

The RBAC system provides:
- ✅ **64 granular permissions** across 8 categories
- ✅ **9 predefined roles** for common use cases
- ✅ **4-layer security** (middleware, layout, page, server action)
- ✅ **Flexible role assignment** per customer
- ✅ **Easy to extend** with new permissions and roles

This ensures secure, scalable, and maintainable access control for your e-commerce platform! 🔒

