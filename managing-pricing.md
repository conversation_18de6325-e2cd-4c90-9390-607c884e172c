Excellent question. This moves us from data ingestion to the core user experience and business logic of your application.

Let's address your specific question first, and then lay out a comprehensive strategy.

### The Short Answer: No, Absolutely Not.

You should **never** show the price in Chinese Yuan (CNY) to all your users.

Displaying prices in a foreign currency is one of the fastest ways to lose a customer. It creates:
*   **Cognitive Load:** The user has to stop and mentally calculate the price in their own currency.
*   **Friction and Distrust:** It feels unprofessional and makes the user uncertain about the final cost.
*   **Cart Abandonment:** Most users will simply leave rather than do the math or risk paying an unknown amount.

**The Goal:** The user should always see a final, understandable price in their own local or preferred currency (e.g., USD, EUR, BRL).

---

### The Complete Strategy: From Cost to Customer Price

Here is a robust, step-by-step workflow for handling pricing and currency conversion, which your current database schema is already well-equipped to handle.

Think of the process in four distinct layers:

1.  **Cost Price (in CNY):** The raw price from the marketplace.
2.  **Base Selling Price (in CNY):** Your cost after applying your business markups.
3.  **Converted Selling Price (in User's Currency):** The price after currency conversion.
4.  **Display Price:** The final, customer-friendly price shown in the UI.

Here’s the detailed workflow:

#### Step 1: Determine the User's Currency

On the **backend**, before you even query for products, you need to know which currency to show the user. You can determine this in a priority order:

1.  **Logged-in User:** Check the `customers` table for their `preferred_currency`. This is the most reliable source.
2.  **Geo-location (IP Address):** If the user is not logged in, use their IP address to guess their country and default currency.
3.  **Browser/OS Locale:** Use the `Accept-Language` header from the browser as another fallback.
4.  **Default:** If all else fails, default to a major currency like USD.

Your application should also have a **currency switcher** in the UI, allowing users to override the default and choose their preferred currency.

#### Step 2: Fetch Data and Rules on the Backend

When a user requests a product page, your server-side code (e.g., a Next.js API route or `getServerSideProps`) should perform these actions:

1.  **Fetch the Base Price:** Get the product's price range from the `offers` or `variants` table. This price is your **Cost Price in CNY**.
2.  **Fetch Applicable Pricing Rules:** Query the `pricing_rules` table to find all active rules that match this product (by marketplace, category, or globally). Remember to respect the `priority` field.
3.  **Fetch the Exchange Rate:** Get the latest exchange rate from CNY to the user's target currency. **This is critical.** You should use a third-party API for this.
    *   **Service Examples:** ExchangeRate-API, Open Exchange Rates.
    *   **Best Practice:** Do **not** call this API for every single product request. Fetch the rates once and **cache them** in your database or a service like Redis for a few hours. A daily or semi-daily update is usually sufficient.

#### Step 3: Calculate the Final Price on the Backend

This is the core business logic. It should always happen on the server to ensure consistency and security.

Let's walk through an example for a US-based user.

*   **Product:** "Premium Tea Set"
*   **User's Currency:** `USD`
*   **Cached Exchange Rate:** `1 CNY = 0.14 USD`

1.  **Get Cost Price:** You fetch the price from the `offers` table.
    *   `price_low`: **100.00 CNY**

2.  **Apply Pricing Rules:** You find a `pricing_rule` that applies a 50% markup (`markup_type: 'PERCENTAGE'`, `markup_value: 1.5`).
    *   `100.00 CNY * 1.5 = 150.00 CNY`
    *   This **150.00 CNY** is your **Base Selling Price**.

3.  **Convert to User's Currency:** Now, use the exchange rate.
    *   `150.00 CNY * 0.14 USD/CNY = 21.00 USD`
    *   This **$21.00** is your **Converted Selling Price**.

4.  **Apply "Cosmetic" Rounding (Optional):** You might want to make the price more appealing.
    *   Instead of $21.00, you might want to display **$20.99**. This is a simple rounding/formatting step.

#### Step 4: Send the Final Price to the Frontend

Your API should send a clean, simple object to your Next.js frontend. The frontend's job is only to *display* this information, not calculate it.

```json
{
  "product": {
    "id": 123,
    "name": "Premium Tea Set",
    // ... other product details
    "price": {
      "displayAmount": "20.99",
      "currencyCode": "USD",
      "currencySymbol": "$"
    }
  }
}
```

This way, your React components are simple and only concerned with presentation.

### How Your Schema Supports This Perfectly

*   `customers.preferred_currency`: Allows you to store the user's choice.
*   `pricing_rules`: Your rule engine is flexible enough to handle various markup strategies.
*   `orders.currency` and `orders.exchange_rate`: **This is crucial.** When a customer places an order, you must **save the currency and the exact exchange rate used at the moment of the transaction**. This ensures that if you need to process a refund later, you refund the exact amount the customer paid, even if exchange rates have changed.

Your setup is excellent. The key is to implement the server-side logic to connect these pieces together and use a reliable, cached exchange rate service.