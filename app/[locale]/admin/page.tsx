// app/[locale]/admin/page.tsx
// Admin dashboard with stats and recent orders

export const dynamic = 'force-dynamic';

import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { DollarSign, ShoppingCart, Clock, Package } from 'lucide-react';
import { StatCard } from '@/components/admin/StatCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { getDashboardStats } from '@/lib/actions/admin/order.actions';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ORDER_STATUS_CONFIG } from '@/lib/constants';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('dashboard'),
  };
}

export default async function AdminDashboardPage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const stats = await getDashboardStats();

  if ('error' in stats) {
    return (
      <div className="text-center py-12">
        <p className="text-destructive">{stats.error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">{t('dashboard')}</h1>
        <p className="text-muted-foreground mt-2">{t('overview')}</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title={t('stats.totalRevenue')}
          value={formatCurrency(stats.totalRevenue, 'USD')}
          icon={DollarSign}
        />
        <StatCard
          title={t('stats.totalOrders')}
          value={stats.totalOrders}
          icon={ShoppingCart}
        />
        <StatCard
          title={t('stats.pendingOrders')}
          value={stats.pendingOrders}
          icon={Clock}
        />
        <StatCard
          title={t('stats.processingOrders')}
          value={stats.processingOrders}
          icon={Package}
        />
      </div>

      {/* Recent Orders */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>{t('recentOrders')}</CardTitle>
          <Link href={`/${locale}/admin/orders`}>
            <Button variant="outline" size="sm">
              {t('viewAllOrders')}
            </Button>
          </Link>
        </CardHeader>
        <CardContent>
          {stats.recentOrders.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">{t('noOrders')}</p>
          ) : (
            <div className="space-y-4">
              {stats.recentOrders.map((order) => (
                <Link
                  key={order.id}
                  href={`/${locale}/admin/orders/${order.id}`}
                  className="block p-4 border rounded-lg hover:bg-accent transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="font-medium">
                        Order #{order.id.slice(0, 8)}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {order.customer.full_name} ({order.customer.email})
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatDate(order.created)}
                      </p>
                    </div>
                    <div className="text-right space-y-2">
                      <p className="font-bold">
                        {formatCurrency(Number(order.total_amount), order.currency)}
                      </p>
                      <Badge
                        variant={
                          order.status === 'pending'
                            ? 'secondary'
                            : order.status === 'processing'
                            ? 'default'
                            : 'outline'
                        }
                      >
                        {ORDER_STATUS_CONFIG[order.status]?.label || order.status}
                      </Badge>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
