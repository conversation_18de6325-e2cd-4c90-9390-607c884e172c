// app/[locale]/admin/orders/page.tsx
// Admin orders list page with filters

import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { OrdersTable } from '@/components/admin/OrdersTable';
import { getAllOrders } from '@/lib/actions/admin/order.actions';
import { OrderStatus } from '@/app/generated/prisma';

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('orderManagement'),
  };
}

export default async function AdminOrdersPage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const resolvedSearchParams = await searchParams;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const search = typeof resolvedSearchParams.search === 'string' ? resolvedSearchParams.search : undefined;
  const status = typeof resolvedSearchParams.status === 'string' ? resolvedSearchParams.status as OrderStatus : undefined;
  const cursor = typeof resolvedSearchParams.cursor === 'string' ? resolvedSearchParams.cursor : undefined;

  const ordersData = await getAllOrders({ search, status, cursor });

  if ('error' in ordersData) {
    return (
      <div className="text-center py-12">
        <p className="text-destructive">{ordersData.error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">{t('orderManagement')}</h1>
        <p className="text-muted-foreground mt-2">
          {t('viewAllOrders')}
        </p>
      </div>

      {/* Orders Table */}
      <OrdersTable
        orders={ordersData.data}
        total={ordersData.data.length}
        page={1}
        totalPages={ordersData.hasMore ? 2 : 1}
        locale={locale}
      />
    </div>
  );
}
