// app/[locale]/admin/orders/[orderId]/page.tsx
// Admin order detail page with status update

import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { UpdateStatusForm } from '@/components/admin/UpdateStatusForm';
import { getOrderByIdAdmin } from '@/lib/actions/admin/order.actions';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ORDER_STATUS_CONFIG } from '@/lib/constants';
import type { Address } from '@/lib/types';

interface PageProps {
  params: Promise<{ locale: string; orderId: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('orderDetails'),
  };
}

export default async function AdminOrderDetailPage({ params }: PageProps) {
  const { locale, orderId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });
  const orderT = await getTranslations({ locale, namespace: 'order' });

  const order = await getOrderByIdAdmin(orderId);

  if ('error' in order) {
    notFound();
  }

  const shippingAddress = order.shipping_address as unknown as Address;

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/admin/orders`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {orderT('backToOrders')}
        </Button>
      </Link>

      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">
            {orderT('order')} #{order.id.slice(0, 8)}
          </h1>
          <p className="text-muted-foreground mt-2">
            {orderT('placed')}: {formatDate(order.created)}
          </p>
        </div>
        <Badge
          variant={
            order.status === 'pending'
              ? 'secondary'
              : order.status === 'processing'
              ? 'default'
              : 'outline'
          }
        >
          {ORDER_STATUS_CONFIG[order.status]?.label || order.status}
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Items */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{orderT('orderItems')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.order_items.map((item) => {
                  const productName = item.product?.translations?.[0]?.name || item.product?.original_name || 'Product';
                  const variantName = item.variant?.translations?.[0]?.variant_name;

                  return (
                    <div key={item.id} className="flex justify-between items-start border-b pb-4 last:border-0">
                      <div className="flex-1">
                        <p className="font-medium">{productName}</p>
                        {variantName && (
                          <p className="text-sm text-muted-foreground">{variantName}</p>
                        )}
                        <p className="text-sm text-muted-foreground">
                          {orderT('quantity')}: {Number(item.quantity)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          {formatCurrency(Number(item.price_per_unit), order.currency)}
                        </p>
                        <p className="text-sm text-muted-foreground">{orderT('each')}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Payment Status */}
          <Card>
            <CardHeader>
              <CardTitle>{t('paymentStatus')}</CardTitle>
            </CardHeader>
            <CardContent>
              {order.payments && order.payments.length > 0 ? (
                <div className="space-y-4">
                  {order.payments.map((payment) => (
                    <div key={payment.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div>
                        <p className="font-medium">
                          {formatCurrency(Number(payment.amount), payment.currency)}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {t('transactionId')}: {payment.transaction_id.slice(0, 16)}...
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {t('method')}: {payment.payment_method}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge
                          variant={
                            payment.status === 'succeeded'
                              ? 'default'
                              : payment.status === 'pending'
                              ? 'secondary'
                              : 'destructive'
                          }
                        >
                          {t(`status${payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}`)}
                        </Badge>
                        <Link href={`/${locale}/admin/payments/${payment.id}`}>
                          <Button variant="ghost" size="sm" className="ml-2">
                            {t('view')}
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-muted-foreground mb-4">{t('awaitingPayment')}</p>
                  <Link href={`/${locale}/admin/orders/${order.id}/payment`}>
                    <Button>
                      {t('recordPayment')}
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Update Status Form */}
          <Card>
            <CardHeader>
              <CardTitle>{t('updateStatus')}</CardTitle>
            </CardHeader>
            <CardContent>
              <UpdateStatusForm
                orderId={order.id}
                currentStatus={order.status}
                currentTrackingNumber={''}
                locale={locale}
              />
            </CardContent>
          </Card>
        </div>

        {/* Order Summary & Customer Info */}
        <div className="space-y-6">
          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle>{orderT('orderSummary')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">{orderT('subtotal')}</span>
                <span className="font-medium">
                  {formatCurrency(Number(order.total_amount) - Number(order.shipping_cost), order.currency)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">{orderT('shipping')}</span>
                <span className="font-medium">
                  {formatCurrency(Number(order.shipping_cost), order.currency)}
                </span>
              </div>
              <div className="flex justify-between pt-2 border-t">
                <span className="font-bold">{orderT('total')}</span>
                <span className="font-bold text-lg">
                  {formatCurrency(Number(order.total_amount), order.currency)}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Address */}
          <Card>
            <CardHeader>
              <CardTitle>{orderT('shippingAddress')}</CardTitle>
            </CardHeader>
            <CardContent>
              {shippingAddress ? (
                <div className="space-y-1 text-sm">
                  <p className="font-medium">{shippingAddress.fullName}</p>
                  <p>{shippingAddress.phone}</p>
                  <p>{shippingAddress.addressLine1}</p>
                  {shippingAddress.addressLine2 && <p>{shippingAddress.addressLine2}</p>}
                  <p>
                    {shippingAddress.city}, {shippingAddress.state} {shippingAddress.postalCode}
                  </p>
                  <p>{shippingAddress.country}</p>
                </div>
              ) : (
                <p className="text-muted-foreground">{orderT('noAddress')}</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
