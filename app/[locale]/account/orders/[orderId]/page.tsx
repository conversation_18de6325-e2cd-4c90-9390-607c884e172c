// app/[locale]/account/orders/[orderId]/page.tsx
// Order detail page

import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { getOrderById } from '@/lib/actions/order.actions';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ORDER_STATUS_CONFIG } from '@/lib/constants';
import type { Address } from '@/lib/types';

interface PageProps {
  params: Promise<{ locale: string; orderId: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale, orderId } = await params;
  const t = await getTranslations({ locale, namespace: 'order' });

  return {
    title: `${t('order')} #${orderId.slice(0, 8)}`,
  };
}

export default async function OrderDetailPage({ params }: PageProps) {
  const { locale, orderId } = await params;
  const t = await getTranslations({ locale, namespace: 'order' });

  const order = await getOrderById(orderId);

  if (!order) {
    notFound();
  }

  const statusConfig = ORDER_STATUS_CONFIG[order.status as keyof typeof ORDER_STATUS_CONFIG];
  const shippingAddress = order.shipping_address as unknown as Address;

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/account/orders`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('backToOrders')}
        </Button>
      </Link>

      {/* Order Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">
            {t('order')} #{order.id.slice(0, 8)}
          </h1>
          <p className="text-muted-foreground mt-1">
            {t('placed')}: {formatDate(order.created)}
          </p>
        </div>
        <Badge variant={statusConfig?.color === 'green' ? 'default' : 'secondary'} className="text-base px-4 py-2">
          {statusConfig?.label || order.status}
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Items */}
        <div className="lg:col-span-2 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('orderItems')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {order.order_items.map((item) => {
                const translation = item.product?.translations[0];
                const productName = translation?.name || item.product?.original_name || 'Product';
                const imageUrl = item.product?.product_images[0]?.image_url;
                const variantTranslation = item.variant?.translations[0];
                const variantName = variantTranslation?.variant_name || item.variant?.original_variant_name;

                return (
                  <div key={item.id} className="flex gap-4">
                    {/* Image */}
                    <div className="relative w-20 h-20 flex-shrink-0 rounded-md overflow-hidden bg-muted">
                      {imageUrl ? (
                        <Image
                          src={imageUrl}
                          alt={productName}
                          fill
                          className="object-cover"
                          sizes="80px"
                        />
                      ) : (
                        <div className="flex h-full items-center justify-center text-xs text-muted-foreground">
                          No Image
                        </div>
                      )}
                    </div>

                    {/* Details */}
                    <div className="flex-1">
                      <p className="font-semibold">{productName}</p>
                      {variantName && (
                        <p className="text-sm text-muted-foreground">{variantName}</p>
                      )}
                      <p className="text-sm text-muted-foreground mt-1">
                        {t('quantity')}: {item.quantity.toString()}
                      </p>
                    </div>

                    {/* Price */}
                    <div className="text-right">
                      <p className="font-semibold">
                        {formatCurrency(Number(item.price_per_unit) * Number(item.quantity), order.currency)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatCurrency(Number(item.price_per_unit), order.currency)} {t('each')}
                      </p>
                    </div>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        </div>

        {/* Order Summary & Shipping */}
        <div className="lg:col-span-1 space-y-4">
          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle>{t('orderSummary')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t('subtotal')}</span>
                <span>
                  {formatCurrency(
                    Number(order.total_amount) - Number(order.shipping_cost || 0),
                    order.currency
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t('shipping')}</span>
                <span>{formatCurrency(Number(order.shipping_cost || 0), order.currency)}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-bold">
                <span>{t('total')}</span>
                <span>{formatCurrency(Number(order.total_amount), order.currency)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Address */}
          <Card>
            <CardHeader>
              <CardTitle>{t('shippingAddress')}</CardTitle>
            </CardHeader>
            <CardContent>
              {shippingAddress ? (
                <div className="text-sm space-y-1">
                  <p className="font-semibold">{shippingAddress.fullName}</p>
                  <p>{shippingAddress.phone}</p>
                  <p>{shippingAddress.addressLine1}</p>
                  {shippingAddress.addressLine2 && <p>{shippingAddress.addressLine2}</p>}
                  <p>
                    {shippingAddress.city}, {shippingAddress.state} {shippingAddress.postalCode}
                  </p>
                  <p>{shippingAddress.country}</p>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">{t('noAddress')}</p>
              )}
            </CardContent>
          </Card>

          {/* Payment Status */}
          {order.payments && order.payments.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>{t('paymentStatus')}</CardTitle>
              </CardHeader>
              <CardContent>
                {order.payments.map((payment) => (
                  <div key={payment.id} className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">{t('method')}</span>
                      <span className="font-medium">{payment.payment_method}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">{t('status')}</span>
                      <Badge variant="secondary">{payment.status}</Badge>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
