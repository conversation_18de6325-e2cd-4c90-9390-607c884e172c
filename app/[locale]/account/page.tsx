// app/[locale]/account/page.tsx
// Account dashboard page

import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { Package, User, MapPin } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { getUserOrders } from '@/lib/actions/order.actions';
import { getUserAddresses } from '@/lib/actions/user.actions';
import { Badge } from '@/components/ui/badge';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ORDER_STATUS_CONFIG } from '@/lib/constants';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'account' });

  return {
    title: t('dashboard'),
  };
}

export default async function AccountDashboardPage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'account' });

  const user = await getCurrentUser();
  const ordersData = await getUserOrders(undefined, 3);
  const addresses = await getUserAddresses();

  const recentOrders = ordersData?.orders.slice(0, 3) || [];

  return (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-primary/10 rounded-full">
                <Package className="h-6 w-6 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t('totalOrders')}</p>
                <p className="text-2xl font-bold">{ordersData?.orders.length || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-primary/10 rounded-full">
                <User className="h-6 w-6 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t('accountStatus')}</p>
                <p className="text-2xl font-bold">{t('active')}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-primary/10 rounded-full">
                <MapPin className="h-6 w-6 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t('savedAddresses')}</p>
                <p className="text-2xl font-bold">{addresses?.length || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Orders */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{t('recentOrders')}</CardTitle>
            <Link href={`/${locale}/account/orders`}>
              <Button variant="outline" size="sm">
                {t('viewAll')}
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {recentOrders.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>{t('noOrders')}</p>
              <Link href={`/${locale}/products`}>
                <Button className="mt-4">{t('startShopping')}</Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {recentOrders.map((order) => {
                const statusConfig = ORDER_STATUS_CONFIG[order.status as keyof typeof ORDER_STATUS_CONFIG];
                return (
                  <div
                    key={order.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <p className="font-semibold">Order #{order.id.slice(0, 8)}</p>
                        <Badge variant={statusConfig?.color === 'green' ? 'default' : 'secondary'}>
                          {statusConfig?.label || order.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(order.created)}
                      </p>
                      <p className="text-sm">
                        {order.order_items.length} {t('items')} • {formatCurrency(Number(order.total_amount), order.currency)}
                      </p>
                    </div>
                    <Link href={`/${locale}/account/orders/${order.id}`}>
                      <Button variant="outline" size="sm">
                        {t('viewDetails')}
                      </Button>
                    </Link>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>{t('profile')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {t('manageProfileDescription')}
            </p>
            <Link href={`/${locale}/account/profile`}>
              <Button variant="outline" className="w-full">
                {t('editProfile')}
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('orders')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {t('viewOrdersDescription')}
            </p>
            <Link href={`/${locale}/account/orders`}>
              <Button variant="outline" className="w-full">
                {t('viewOrders')}
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
