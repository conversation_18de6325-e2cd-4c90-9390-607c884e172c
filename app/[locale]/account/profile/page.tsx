// app/[locale]/account/profile/page.tsx
// Profile management page

export const dynamic = 'force-dynamic';

import { getTranslations } from 'next-intl/server';
import { getUserProfile, getUserAddresses } from '@/lib/actions/user.actions';
import { ProfileForm } from '@/components/account/ProfileForm';
import { AddressList } from '@/components/account/AddressList';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'account' });

  return {
    title: t('profile'),
  };
}

export default async function ProfilePage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'account' });

  const user = await getUserProfile();
  const addresses = await getUserAddresses();

  if (!user) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Profile Information */}
      <Card>
        <CardHeader>
          <CardTitle>{t('profileInformation')}</CardTitle>
        </CardHeader>
        <CardContent>
          <ProfileForm user={user} locale={locale} />
        </CardContent>
      </Card>

      {/* Addresses */}
      <Card>
        <CardHeader>
          <CardTitle>{t('savedAddresses')}</CardTitle>
        </CardHeader>
        <CardContent>
          <AddressList addresses={addresses || []} locale={locale} />
        </CardContent>
      </Card>
    </div>
  );
}
