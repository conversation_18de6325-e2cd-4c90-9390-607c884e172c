// app/[locale]/(checkout)/checkout/shipping/page.tsx
// Shipping address selection page

export const dynamic = 'force-dynamic';

import { redirect } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { ShippingForm } from '@/components/checkout/ShippingForm';
import { getUserAddresses } from '@/lib/actions/user.actions';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'checkout' });

  return {
    title: t('shippingAddress'),
  };
}

export default async function ShippingPage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'checkout' });

  const addresses = await getUserAddresses();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">{t('shippingAddress')}</h1>
          <p className="text-muted-foreground">{t('selectShippingAddress')}</p>
        </div>

        <ShippingForm addresses={addresses || []} locale={locale} />
      </div>
    </div>
  );
}

