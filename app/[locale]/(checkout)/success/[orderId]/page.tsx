// app/[locale]/(checkout)/success/[orderId]/page.tsx
// Order success page

import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { CheckCircle, Package } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { getOrderById } from '@/lib/actions/order.actions';
import { formatCurrency } from '@/lib/utils';

interface PageProps {
  params: Promise<{ locale: string; orderId: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'checkout' });

  return {
    title: t('orderSuccess'),
  };
}

export default async function OrderSuccessPage({ params }: PageProps) {
  const { locale, orderId } = await params;
  const t = await getTranslations({ locale, namespace: 'checkout' });

  const order = await getOrderById(orderId);

  if (!order) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-2xl mx-auto">
        {/* Success Icon */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-green-100 dark:bg-green-900 mb-4">
            <CheckCircle className="h-12 w-12 text-green-600 dark:text-green-400" />
          </div>
          <h1 className="text-3xl font-bold mb-2">{t('orderPlaced')}</h1>
          <p className="text-muted-foreground">
            {t('orderNumber')}: <span className="font-semibold">#{order.id.slice(0, 8)}</span>
          </p>
        </div>

        {/* Order Summary */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>{t('orderSummary')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-muted-foreground">{t('items')}</span>
              <span className="font-medium">{order.order_items.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">{t('total')}</span>
              <span className="font-bold text-lg">
                {formatCurrency(Number(order.total_amount), order.currency)}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>{t('whatNext')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm">
            <div className="flex gap-3">
              <Package className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-semibold">{t('step1Title')}</p>
                <p className="text-muted-foreground">{t('step1Description')}</p>
              </div>
            </div>
            <div className="flex gap-3">
              <Package className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-semibold">{t('step2Title')}</p>
                <p className="text-muted-foreground">{t('step2Description')}</p>
              </div>
            </div>
            <div className="flex gap-3">
              <Package className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-semibold">{t('step3Title')}</p>
                <p className="text-muted-foreground">{t('step3Description')}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Link href={`/${locale}/account/orders/${order.id}`} className="flex-1">
            <Button variant="outline" className="w-full">
              {t('viewOrder')}
            </Button>
          </Link>
          <Link href={`/${locale}/products`} className="flex-1">
            <Button className="w-full">{t('continueShopping')}</Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
