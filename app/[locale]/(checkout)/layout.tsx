// app/[locale]/(checkout)/layout.tsx
// Checkout layout

import { redirect } from 'next/navigation';
import { getCurrentUser } from '@/lib/actions/auth.actions';

interface LayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function CheckoutLayout({ children, params }: LayoutProps) {
  const { locale } = await params;
  const user = await getCurrentUser();

  // Redirect to login if not authenticated
  if (!user) {
    redirect(`/${locale}/login?redirect=/${locale}/shipping`);
  }

  return <>{children}</>;
}
