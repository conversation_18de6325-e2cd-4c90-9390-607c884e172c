// app/[locale]/(checkout)/payment/page.tsx
// Payment page with order creation

import { getTranslations } from 'next-intl/server';
import { redirect } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { createOrder } from '@/lib/actions/order.actions';
import { trackPurchase } from '@/lib/actions/user-activity.actions';
import type { Address } from '@/lib/types';
import { getCurrentUser } from '@/lib/actions/auth.actions';

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{
    cart?: string;
    shipping?: string;
  }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'checkout' });

  return {
    title: t('paymentMethod'),
  };
}

export default async function PaymentPage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const search = await searchParams;
  const t = await getTranslations({ locale, namespace: 'checkout' });

  // Get user and validate authentication
  const user = await getCurrentUser();
  if (!user) {
    redirect(`/${locale}/login`);
  }

  // Parse cart and shipping from search params
  let cartItems: Array<{id: number; productId: number; variantId?: number; quantity: number; productName: string; productSlug: string; variantName?: string; price: number; currency: string; imageUrl?: string; marketplace: string}> = [];
  let shippingAddress: Address = {} as Address;

  try {
    if (search.cart) {
      cartItems = JSON.parse(decodeURIComponent(search.cart));
    }
    if (search.shipping) {
      shippingAddress = JSON.parse(decodeURIComponent(search.shipping));
    }
  } catch (error) {
    // Invalid cart/shipping data
    redirect(`/${locale}/cart`);
  }

  if (!cartItems.length || !shippingAddress) {
    redirect(`/${locale}/cart`);
  }

  // Calculate totals (simplified - in real app this would be more complex)
  const subtotal = cartItems.reduce((sum: number, item) => sum + item.price * item.quantity, 0);
  const shippingCost = 15.00; // Fixed shipping cost
  const total = subtotal + shippingCost;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">{t('paymentMethod')}</h1>
          <p className="text-muted-foreground">{t('paymentInstructions')}</p>
        </div>

        {/* Order Summary */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>{t('orderSummary')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {cartItems.map((item) => (
                <div key={item.id} className="flex justify-between">
                  <span>{item.productName} (x{item.quantity})</span>
                  <span>${(item.price * item.quantity).toFixed(2)}</span>
                </div>
              ))}
              <div className="border-t pt-2">
                <div className="flex justify-between">
                  <span>{t('subtotal')}</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('shipping')}</span>
                  <span>${shippingCost.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-bold text-lg">
                  <span>{t('total')}</span>
                  <span>${total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Instructions */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>{t('paymentInstructions')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                {t('paymentInstructionsText')}
              </p>
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-semibold mb-2">{t('bankTransferDetails')}</h3>
                <div className="space-y-1 text-sm">
                  <p><strong>{t('bankName')}:</strong> Example Bank</p>
                  <p><strong>{t('accountName')}:</strong> MaoMao Trading Co.</p>
                  <p><strong>{t('accountNumber')}:</strong> **********</p>
                  <p><strong>{t('swiftCode')}:</strong> EXAMPLSW</p>
                  <p><strong>{t('reference')}:</strong> Your Order Reference</p>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                {t('paymentNote')}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Place Order Button */}
        <div className="flex justify-center">
          <form
            action={async () => {
              'use server';

              // Track purchase activities for all cart items
              const sessionId = `session_${Date.now()}_${Math.random()}`;
              for (const item of cartItems) {
                await trackPurchase(item.productId, item.marketplace, sessionId);
              }

              // Create the order
              const orderData = {
                items: cartItems,
                shippingAddress,
                currency: 'USD',
                exchangeRate: 1.0, // Simplified
                shippingCost,
              };

              const result = await createOrder(orderData);

              if (result.success && result.orderId) {
                redirect(`/${locale}/checkout/success/${result.orderId}`);
              } else {
                // Handle error - in real app, show error message
                redirect(`/${locale}/cart?error=payment_failed`);
              }
            }}
          >
            <Button type="submit" size="lg" className="px-8">
              {t('placeOrder')}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}
