// app/[locale]/(marketing)/cart/page.tsx
// Shopping cart page

import { getTranslations } from 'next-intl/server';
import { CartClient } from '@/components/cart/CartClient';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'cart' });

  return {
    title: t('title'),
  };
}

export default async function CartPage({ params }: PageProps) {
  const { locale } = await params;

  return (
    <div className="container mx-auto px-4 py-8">
      <CartClient locale={locale} />
    </div>
  );
}
